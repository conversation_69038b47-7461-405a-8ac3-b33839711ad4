# 模型文件和权重文件
*.pth
*.pt
*.bin
*.safetensors
*.h5
*.ckpt
*.pkl
*.model
*.weights

# 模型目录
deepseek-coder-1.3b-base/
Llama-2-7b-hf/

# 缓存文件
c4_cache/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# 分析结果文件
final_sparse_model/
results/
analyze_pruning_results.py

# LaTeX 编译文件
*.aux
*.log
*.out
*.synctex.gz
*.toc
*.fls
*.fdb_latexmk
*.bbl
*.blg
*.bcf
*.run.xml
_minted-*/

# 编辑器和IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db
desktop.ini

# 临时文件
*.tmp
*.temp
*.bak

# 数据文件
data/
*.zip
*.tar.gz
*.rar
*.7z

# 虚拟环境
venv/
env/
.env
.venv/

# <PERSON>pyter Notebook 检查点
.ipynb_checkpoints/

# 测试覆盖率
.coverage

# 分发/打包
dist/
build/
*.egg-info/

# 日志文件
*.log