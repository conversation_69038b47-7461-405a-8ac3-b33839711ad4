#!/usr/bin/env python3
"""
Comprehensive Pruning Analysis for LOSA
Combines multi-round sparsity analysis and reconstruction loss analysis
"""

import re
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import numpy as np
import pandas as pd
import seaborn as sns
from collections import defaultdict
import os

# Set style for high-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Create img directory if it doesn't exist
os.makedirs('./img', exist_ok=True)

def parse_multi_round_log(filename):
    """Parse the log file to extract multi-round pruning data"""
    
    rounds_data = []
    training_data = []
    final_sparsity_data = []
    predictor_data = []
    reconstruction_data = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract training epochs and loss data
    epoch_pattern = r"{'loss': ([\d.]+), 'learning_rate': [\d.e-]+, 'epoch': ([\d.]+)}"
    epoch_matches = re.findall(epoch_pattern, content)
    
    current_round = 0
    for loss, epoch in epoch_matches:
        if float(epoch) == 1.0:  # End of training round
            current_round += 1
        training_data.append({
            'round': current_round,
            'epoch': float(epoch),
            'loss': float(loss)
        })
    
    # Extract round-wise sparsity progression
    round_sparsity_pattern = r'训练后稀疏性检查.*?\n((?:layer \d+ sparsity [\d.]+\n)+)'
    round_matches = re.findall(round_sparsity_pattern, content, re.DOTALL)
    
    for round_idx, sparsity_block in enumerate(round_matches):
        layer_pattern = r'layer (\d+) sparsity ([\d.]+)'
        layer_matches = re.findall(layer_pattern, sparsity_block)
        
        for layer_idx, sparsity in layer_matches:
            rounds_data.append({
                'round': round_idx + 1,
                'layer': int(layer_idx),
                'sparsity': float(sparsity) * 100  # Convert to percentage
            })
    
    # Extract final detailed sparsity data (from the last round)
    final_pattern = r'第(\d+)层剪枝完成汇总:.*?层实际稀疏度: ([\d.]+)%'
    final_matches = re.findall(final_pattern, content)
    
    for layer_idx, sparsity in final_matches:
        final_sparsity_data.append({
            'layer': int(layer_idx),
            'final_sparsity': float(sparsity)
        })
    
    # Extract ALL predictor usage statistics (multi-round)
    predictor_pattern = r'层(\d+)-(.*?): 选择预测器(\w+), 分数=\[([\d.]+), ([\d.]+), ([\d.]+)\], 概率=\[([\d.]+), ([\d.]+), ([\d.]+)\]'
    predictor_matches = re.findall(predictor_pattern, content)
    
    # Group predictor data by rounds based on position in file
    total_matches = len(predictor_matches)
    components_per_layer = 7
    layers_per_round = 24
    decisions_per_round = components_per_layer * layers_per_round
    
    for idx, match in enumerate(predictor_matches):
        layer_num, component, predictor, score1, score2, score3, prob1, prob2, prob3 = match
        
        # Calculate which round this decision belongs to
        round_num = (idx // decisions_per_round) + 1
        
        simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
        predictor_data.append({
            'round': round_num,
            'layer': int(layer_num),
            'component': simple_component,
            'selected_predictor': predictor,
            'scores': [float(score1), float(score2), float(score3)],
            'probabilities': [float(prob1), float(prob2), float(prob3)]
        })
    
    # Extract reconstruction loss data
    recon_pattern = r"重构损失:\s*\[([\d.,\s']+)\]"
    recon_matches = re.findall(recon_pattern, content)
    
    round_num = 1
    for match in recon_matches:
        # Clean and parse the loss values
        loss_str = match.replace("'", "").replace(" ", "")
        loss_values = [float(x) for x in loss_str.split(',') if x.strip()]
        
        for layer_idx, loss_value in enumerate(loss_values):
            reconstruction_data.append({
                'round': round_num,
                'layer': layer_idx,
                'reconstruction_loss': loss_value
            })
        
        round_num += 1
    
    return (pd.DataFrame(rounds_data), pd.DataFrame(training_data), 
            pd.DataFrame(final_sparsity_data), pd.DataFrame(predictor_data),
            pd.DataFrame(reconstruction_data))

def create_multi_round_sparsity_evolution(rounds_df):
    """Create visualization showing sparsity evolution across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # 1. Sparsity evolution heatmap
    pivot_data = rounds_df.pivot(index='layer', columns='round', values='sparsity')
    
    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlBu_r', 
                center=50, vmin=25, vmax=52, ax=ax1,
                cbar_kws={'label': 'Sparsity (%)'})
    
    ax1.set_title('Multi-Round Sparsity Evolution\n(Layer vs Round)', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Layer Index', fontsize=14)
    
    # 2. Layer-wise sparsity progression lines
    colors = plt.cm.viridis(np.linspace(0, 1, len(pivot_data.index)))
    
    for i, layer in enumerate(pivot_data.index):
        if layer % 4 == 0:  # Show every 4th layer to avoid clutter
            ax2.plot(pivot_data.columns, pivot_data.loc[layer], 'o-', 
                    color=colors[i], linewidth=2, markersize=6, 
                    label=f'Layer {layer}', alpha=0.8)
    
    ax2.set_title('Sparsity Progression by Layer', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Sparsity (%)', fontsize=14)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(20, 55)
    
    plt.tight_layout()
    plt.savefig('./img/multi_round_sparsity_evolution.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_training_loss_analysis(training_df):
    """Create optimized visualization showing training loss across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # 1. Simplified loss progression - show only key rounds
    rounds = sorted(training_df['round'].unique())
    
    # Calculate average loss per round instead of all individual lines
    round_stats = []
    for round_num in rounds:
        round_data = training_df[training_df['round'] == round_num]
        if len(round_data) > 0:
            avg_loss = round_data['loss'].mean()
            min_loss = round_data['loss'].min()
            max_loss = round_data['loss'].max()
            final_loss = round_data[round_data['epoch'] == round_data['epoch'].max()]['loss'].iloc[0]
            round_stats.append({
                'round': round_num,
                'avg_loss': avg_loss,
                'min_loss': min_loss,
                'max_loss': max_loss,
                'final_loss': final_loss
            })
    
    round_stats_df = pd.DataFrame(round_stats)
    
    # Plot average loss with error bars
    ax1.errorbar(round_stats_df['round'], round_stats_df['avg_loss'], 
                yerr=[round_stats_df['avg_loss'] - round_stats_df['min_loss'],
                      round_stats_df['max_loss'] - round_stats_df['avg_loss']], 
                fmt='o-', linewidth=3, markersize=8, capsize=5, capthick=2,
                color='darkblue', label='Average Loss ± Range')
    
    # Plot final loss trend
    ax1.plot(round_stats_df['round'], round_stats_df['final_loss'], 
             's-', linewidth=2, markersize=6, color='red', alpha=0.8,
             label='Final Loss per Round')
    
    ax1.set_title('Training Loss Analysis Across Rounds', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Training Loss', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Loss improvement analysis
    loss_improvements = []
    for i in range(1, len(round_stats_df)):
        prev_loss = round_stats_df.iloc[i-1]['final_loss']
        curr_loss = round_stats_df.iloc[i]['final_loss']
        improvement = ((prev_loss - curr_loss) / prev_loss) * 100
        loss_improvements.append(improvement)
    
    colors = ['green' if x > 0 else 'red' for x in loss_improvements]
    bars = ax2.bar(range(2, len(rounds) + 1), loss_improvements, 
                   color=colors, alpha=0.7, edgecolor='black')
    
    # Add value labels on bars
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height > 0 else -1),
                f'{height:.1f}%', ha='center', va='bottom' if height > 0 else 'top', 
                fontweight='bold')
    
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax2.set_title('Loss Improvement Between Rounds', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Round Transition', fontsize=14)
    ax2.set_ylabel('Loss Improvement (%)', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('./img/training_loss_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_sparsity_convergence_analysis(rounds_df):
    """Analyze how sparsity converges to target across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # 1. Average sparsity per round
    avg_sparsity = rounds_df.groupby('round')['sparsity'].agg(['mean', 'std']).reset_index()
    
    ax1.errorbar(avg_sparsity['round'], avg_sparsity['mean'], 
                yerr=avg_sparsity['std'], fmt='o-', linewidth=3, 
                markersize=8, capsize=5, capthick=2)
    
    # Add target line
    ax1.axhline(y=50, color='red', linestyle='--', linewidth=2, 
               label='Target Sparsity (50%)', alpha=0.8)
    
    ax1.set_title('Sparsity Convergence to Target', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Average Sparsity (%) ± Std', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(20, 55)
    
    # 2. Sparsity variance across layers per round
    sparsity_variance = rounds_df.groupby('round')['sparsity'].var().reset_index()
    
    bars = ax2.bar(sparsity_variance['round'], sparsity_variance['sparsity'], 
                   color='lightcoral', alpha=0.7, edgecolor='darkred')
    
    # Add value labels
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{height:.2f}', ha='center', va='bottom', fontweight='bold')
    
    ax2.set_title('Sparsity Variance Across Layers by Round', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Sparsity Variance', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('./img/sparsity_convergence_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_multi_round_predictor_analysis(predictor_df):
    """Create comprehensive multi-round predictor selection analysis"""

    # Create figure with 6 subplots for comprehensive analysis
    fig = plt.figure(figsize=(24, 16))
    gs = fig.add_gridspec(3, 3, height_ratios=[2, 1.5, 1.5], width_ratios=[2, 1, 1])

    # 1. Multi-round predictor evolution heatmap (top, spanning 2 columns)
    ax1 = fig.add_subplot(gs[0, :2])

    # Create a comprehensive predictor matrix across all rounds
    rounds = sorted(predictor_df['round'].unique())
    layers = sorted(predictor_df['layer'].unique())
    components = sorted(predictor_df['component'].unique())

    # Create a matrix showing predictor changes across rounds
    predictor_evolution = []
    for round_num in rounds:
        round_data = predictor_df[predictor_df['round'] == round_num]
        for layer in layers:
            layer_data = round_data[round_data['layer'] == layer]
            for component in components:
                comp_data = layer_data[layer_data['component'] == component]
                if len(comp_data) > 0:
                    predictor = comp_data['selected_predictor'].iloc[0]
                    predictor_evolution.append({
                        'round': round_num,
                        'layer': layer,
                        'component': component,
                        'position': layer * len(components) + components.index(component),
                        'predictor': predictor
                    })

    evolution_df = pd.DataFrame(predictor_evolution)

    # Create pivot for heatmap
    pivot_evolution = evolution_df.pivot(index='position', columns='round', values='predictor')

    # Map predictors to numbers for visualization
    predictor_map = {'snip': 0, 'wanda': 1, 'dolphin': 2}
    pivot_evolution_num = pivot_evolution.replace(predictor_map)

    # Create custom colormap
    colors = ['#E74C3C', '#3498DB', '#2ECC71']  # snip=red, wanda=blue, dolphin=green
    from matplotlib.colors import ListedColormap
    cmap = ListedColormap(colors)

    # Plot heatmap
    im = ax1.imshow(pivot_evolution_num.values, cmap=cmap, aspect='auto', vmin=0, vmax=2,
                    interpolation='nearest')

    ax1.set_title('Multi-Round Predictor Evolution\n(All Layers × Components × Rounds)',
                  fontsize=16, fontweight='bold', pad=20)
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Layer × Component Position', fontsize=14)

    # Set ticks
    ax1.set_xticks(range(len(rounds)))
    ax1.set_xticklabels([f'R{r}' for r in rounds])

    # Add position labels for key layers
    position_labels = []
    for i in range(0, len(layers), 4):  # Every 4th layer
        for j, comp in enumerate(components):
            if j == 0:  # Only label first component of each layer
                pos = i * len(components) + j
                position_labels.append((pos, f'L{layers[i]}'))

    if position_labels:
        positions, labels = zip(*position_labels)
        ax1.set_yticks(positions)
        ax1.set_yticklabels(labels)

    # Create legend
    legend_elements = [mpatches.Patch(color=colors[i], label=pred.upper())
                      for i, pred in enumerate(['SNIP', 'WANDA', 'DOLPHIN'])]
    ax1.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1.02, 1))

    # 2. Round-wise predictor distribution (top right)
    ax2 = fig.add_subplot(gs[0, 2])

    round_predictor_counts = predictor_df.groupby(['round', 'selected_predictor']).size().unstack(fill_value=0)
    round_predictor_pct = round_predictor_counts.div(round_predictor_counts.sum(axis=1), axis=0) * 100

    # Stacked area chart
    ax2.stackplot(round_predictor_pct.index,
                 round_predictor_pct.get('snip', 0),
                 round_predictor_pct.get('wanda', 0),
                 round_predictor_pct.get('dolphin', 0),
                 labels=['SNIP', 'WANDA', 'DOLPHIN'], colors=colors, alpha=0.8)

    ax2.set_title('Predictor Usage\nAcross Rounds (%)', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Round')
    ax2.set_ylabel('Percentage')
    ax2.legend(loc='upper right')
    ax2.set_ylim(0, 100)
    ax2.grid(True, alpha=0.3)

    # 3. Component-wise predictor preference (middle left)
    ax3 = fig.add_subplot(gs[1, 0])

    # Aggregate across all rounds for component analysis
    component_predictor = predictor_df.groupby(['component', 'selected_predictor']).size().unstack(fill_value=0)
    component_predictor_pct = component_predictor.div(component_predictor.sum(axis=1), axis=0) * 100

    # Create horizontal stacked bar chart
    component_predictor_pct_ordered = component_predictor_pct.reindex(['snip', 'wanda', 'dolphin'], axis=1, fill_value=0)

    ax3 = component_predictor_pct_ordered.plot(kind='barh', stacked=True, ax=ax3,
                                              color=colors, width=0.8)

    ax3.set_title('Predictor Preference by Component\n(All Rounds Combined)', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Percentage (%)')
    ax3.set_ylabel('Component')

    # Improve y-axis labels
    component_labels = {
        'attn_q_proj': 'Attention Q',
        'attn_k_proj': 'Attention K',
        'attn_v_proj': 'Attention V',
        'attn_o_proj': 'Attention O',
        'mlp_down_proj': 'MLP Down',
        'mlp_gate_proj': 'MLP Gate',
        'mlp_up_proj': 'MLP Up'
    }

    y_labels = [component_labels.get(comp, comp) for comp in component_predictor_pct.index]
    ax3.set_yticklabels(y_labels)

    ax3.legend(title='Predictor', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3, axis='x')
    ax3.set_xlim(0, 100)

    # 4. Predictor confidence evolution (middle center)
    ax4 = fig.add_subplot(gs[1, 1])

    # Calculate average confidence per round per predictor
    confidence_data = []
    for _, row in predictor_df.iterrows():
        max_prob_idx = np.argmax(row['probabilities'])
        confidence_data.append({
            'round': row['round'],
            'predictor': row['selected_predictor'],
            'confidence': row['probabilities'][max_prob_idx]
        })

    confidence_df = pd.DataFrame(confidence_data)
    confidence_avg = confidence_df.groupby(['round', 'predictor'])['confidence'].mean().unstack(fill_value=0)

    for predictor in ['snip', 'wanda', 'dolphin']:
        if predictor in confidence_avg.columns:
            color = colors[['snip', 'wanda', 'dolphin'].index(predictor)]
            ax4.plot(confidence_avg.index, confidence_avg[predictor],
                    'o-', color=color, linewidth=2, markersize=6,
                    label=predictor.upper())

    ax4.set_title('Predictor Confidence\nEvolution', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Round')
    ax4.set_ylabel('Average Confidence')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0.2, 0.6)

    # 5. Final round predictor matrix (middle right)
    ax5 = fig.add_subplot(gs[1, 2])

    # Show final round predictor selection matrix
    final_round = predictor_df['round'].max()
    final_round_data = predictor_df[predictor_df['round'] == final_round]

    final_matrix = final_round_data.pivot(index='component', columns='layer', values='selected_predictor')
    final_matrix_num = final_matrix.replace(predictor_map)

    im5 = ax5.imshow(final_matrix_num.values, cmap=cmap, aspect='auto', vmin=0, vmax=2,
                     interpolation='nearest')

    ax5.set_title(f'Final Round (R{final_round})\nPredictor Matrix', fontsize=14, fontweight='bold')
    ax5.set_xlabel('Layer')
    ax5.set_ylabel('Component')

    # Set ticks for final matrix
    ax5.set_xticks(range(0, len(final_matrix.columns), 4))
    ax5.set_xticklabels(range(0, len(final_matrix.columns), 4))
    ax5.set_yticks(range(len(final_matrix.index)))
    ax5.set_yticklabels([component_labels.get(comp, comp) for comp in final_matrix.index], fontsize=10)

    # 6. Summary statistics (bottom)
    ax6 = fig.add_subplot(gs[2, :])
    ax6.axis('off')

    # Calculate comprehensive statistics
    total_decisions = len(predictor_df)
    total_rounds = len(predictor_df['round'].unique())
    decisions_per_round = total_decisions // total_rounds

    predictor_counts = predictor_df['selected_predictor'].value_counts()

    # Calculate predictor changes between rounds
    changes_between_rounds = 0
    if total_rounds > 1:
        for layer in layers:
            for component in components:
                layer_comp_data = predictor_df[(predictor_df['layer'] == layer) &
                                             (predictor_df['component'] == component)]
                if len(layer_comp_data) > 1:
                    predictors = layer_comp_data['selected_predictor'].tolist()
                    for i in range(1, len(predictors)):
                        if predictors[i] != predictors[i-1]:
                            changes_between_rounds += 1

    summary_text = f"""
Multi-Round Predictor Selection Analysis Summary

Total Decisions: {total_decisions} ({total_rounds} rounds × {decisions_per_round} decisions/round)
Predictor Distribution: SNIP {predictor_counts.get('snip', 0)} ({predictor_counts.get('snip', 0)/total_decisions*100:.1f}%),
                       WANDA {predictor_counts.get('wanda', 0)} ({predictor_counts.get('wanda', 0)/total_decisions*100:.1f}%),
                       DOLPHIN {predictor_counts.get('dolphin', 0)} ({predictor_counts.get('dolphin', 0)/total_decisions*100:.1f}%)

Predictor Changes Between Rounds: {changes_between_rounds} position changes
Stability: {(1 - changes_between_rounds/(len(layers)*len(components)*(total_rounds-1)))*100:.1f}% positions unchanged

Components: {len(components)} per layer | Layers: {len(layers)} | Rounds: {total_rounds}
    """

    ax6.text(0.5, 0.5, summary_text, transform=ax6.transAxes, fontsize=12,
             horizontalalignment='center', verticalalignment='center',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    plt.suptitle('Multi-Round Predictor Selection Analysis',
                 fontsize=20, fontweight='bold', y=0.98)

    plt.tight_layout()
    plt.subplots_adjust(top=0.95, bottom=0.05)
    plt.savefig('./img/multi_round_predictor_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_reconstruction_loss_analysis(recon_df):
    """Create comprehensive reconstruction loss analysis"""

    if len(recon_df) == 0:
        print("No reconstruction loss data found")
        return

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))

    # 1. Reconstruction loss heatmap
    pivot_data = recon_df.pivot(index='layer', columns='round', values='reconstruction_loss')

    sns.heatmap(pivot_data, annot=True, fmt='.4f', cmap='Reds', ax=ax1,
                cbar_kws={'label': 'Reconstruction Loss'})

    ax1.set_title('Reconstruction Loss Across Layers and Rounds', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Layer Index', fontsize=14)

    # 2. Average reconstruction loss per round
    round_stats = recon_df.groupby('round')['reconstruction_loss'].agg(['mean', 'std', 'min', 'max']).reset_index()

    ax2.errorbar(round_stats['round'], round_stats['mean'],
                yerr=round_stats['std'], fmt='o-', linewidth=3,
                markersize=8, capsize=5, capthick=2, color='darkred')

    ax2.fill_between(round_stats['round'], round_stats['min'], round_stats['max'],
                     alpha=0.2, color='red', label='Min-Max Range')

    ax2.set_title('Average Reconstruction Loss per Round', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Pruning Round')
    ax2.set_ylabel('Reconstruction Loss (Mean ± Std)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. Layer-wise reconstruction loss patterns
    layer_stats = recon_df.groupby('layer')['reconstruction_loss'].agg(['mean', 'std']).reset_index()

    bars = ax3.bar(layer_stats['layer'], layer_stats['mean'],
                   yerr=layer_stats['std'], capsize=3,
                   color='lightcoral', alpha=0.7, edgecolor='darkred')

    ax3.set_title('Average Reconstruction Loss by Layer', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Layer Index')
    ax3.set_ylabel('Reconstruction Loss (Mean ± Std)')
    ax3.grid(True, alpha=0.3, axis='y')

    # 4. Reconstruction loss improvement analysis
    round_improvement = []
    rounds = sorted(recon_df['round'].unique())

    for i in range(1, len(rounds)):
        prev_round = recon_df[recon_df['round'] == rounds[i-1]]['reconstruction_loss'].mean()
        curr_round = recon_df[recon_df['round'] == rounds[i]]['reconstruction_loss'].mean()
        improvement = ((prev_round - curr_round) / prev_round) * 100
        round_improvement.append(improvement)

    colors = ['green' if x > 0 else 'red' for x in round_improvement]
    bars = ax4.bar(range(2, len(rounds) + 1), round_improvement,
                   color=colors, alpha=0.7, edgecolor='black')

    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height > 0 else -1),
                f'{height:.1f}%', ha='center', va='bottom' if height > 0 else 'top',
                fontweight='bold')

    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax4.set_title('Reconstruction Loss Improvement Between Rounds', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Round Transition')
    ax4.set_ylabel('Improvement (%)')
    ax4.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    plt.savefig('./img/reconstruction_loss_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main analysis function"""

    print("🔍 Analyzing Multi-Round Progressive Pruning...")

    # Parse the log file
    rounds_df, training_df, final_df, predictor_df, recon_df = parse_multi_round_log('run.log')

    print(f"📊 Data Summary:")
    print(f"   - Pruning rounds: {len(rounds_df['round'].unique()) if len(rounds_df) > 0 else 0}")
    print(f"   - Training epochs: {len(training_df)}")
    print(f"   - Layers analyzed: {len(rounds_df['layer'].unique()) if len(rounds_df) > 0 else 0}")
    print(f"   - Predictor decisions: {len(predictor_df)}")
    print(f"   - Reconstruction loss records: {len(recon_df)}")

    print("\n🎨 Creating visualizations...")

    # Create all visualizations
    if len(rounds_df) > 0:
        create_multi_round_sparsity_evolution(rounds_df)
        create_sparsity_convergence_analysis(rounds_df)

    if len(training_df) > 0:
        create_training_loss_analysis(training_df)

    if len(predictor_df) > 0:
        create_multi_round_predictor_analysis(predictor_df)

    if len(recon_df) > 0:
        create_reconstruction_loss_analysis(recon_df)

    print("\n✅ Comprehensive pruning analysis complete!")
    print("📁 Generated files in ./img directory:")
    print("   - multi_round_sparsity_evolution.png")
    print("   - training_loss_analysis.png")
    print("   - sparsity_convergence_analysis.png")
    print("   - multi_round_predictor_analysis.png")
    print("   - reconstruction_loss_analysis.png")

if __name__ == "__main__":
    main()
