#!/usr/bin/env python3
"""
Comprehensive Multi-round Pruning Analysis for LOSA
Combines multi-round sparsity, training loss, reconstruction loss, and predictor analysis
"""

import re
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import numpy as np
import pandas as pd
import seaborn as sns
from collections import defaultdict
from matplotlib.colors import ListedColormap
import os

# Set style for high-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Create img directory if it doesn't exist
os.makedirs('./img', exist_ok=True)

def parse_comprehensive_log(filename):
    """Parse the log file to extract all multi-round data"""
    
    rounds_data = []
    training_data = []
    final_sparsity_data = []
    predictor_data = []
    reconstruction_data = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract training epochs and loss data
    epoch_pattern = r"{'loss': ([\d.]+), 'learning_rate': [\d.e-]+, 'epoch': ([\d.]+)}"
    epoch_matches = re.findall(epoch_pattern, content)
    
    current_round = 0
    for loss, epoch in epoch_matches:
        if float(epoch) == 1.0:  # End of training round
            current_round += 1
        training_data.append({
            'round': current_round,
            'epoch': float(epoch),
            'loss': float(loss)
        })
    
    # Extract round-wise sparsity progression
    round_sparsity_pattern = r'训练后稀疏性检查.*?\n((?:layer \d+ sparsity [\d.]+\n)+)'
    round_matches = re.findall(round_sparsity_pattern, content, re.DOTALL)
    
    for round_idx, sparsity_block in enumerate(round_matches):
        layer_pattern = r'layer (\d+) sparsity ([\d.]+)'
        layer_matches = re.findall(layer_pattern, sparsity_block)
        
        for layer_idx, sparsity in layer_matches:
            rounds_data.append({
                'round': round_idx + 1,
                'layer': int(layer_idx),
                'sparsity': float(sparsity) * 100  # Convert to percentage
            })
    
    # Extract final detailed sparsity data
    final_pattern = r'第(\d+)层剪枝完成汇总:.*?层实际稀疏度: ([\d.]+)%'
    final_matches = re.findall(final_pattern, content)
    
    for layer_idx, sparsity in final_matches:
        final_sparsity_data.append({
            'layer': int(layer_idx),
            'final_sparsity': float(sparsity)
        })
    
    # Extract multi-round predictor usage statistics
    # Find all predictor selections across all rounds
    predictor_sections = re.findall(r'第(\d+)层剪枝开始.*?第\d+层剪枝完成汇总', content, re.DOTALL)
    
    round_num = 1
    for section in predictor_sections:
        predictor_pattern = r'层(\d+)-(.*?): 选择预测器(\w+), 分数=\[([\d.]+), ([\d.]+), ([\d.]+)\], 概率=\[([\d.]+), ([\d.]+), ([\d.]+)\]'
        predictor_matches = re.findall(predictor_pattern, section)
        
        for match in predictor_matches:
            layer_num, component, predictor, score1, score2, score3, prob1, prob2, prob3 = match
            simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
            predictor_data.append({
                'round': round_num,
                'layer': int(layer_num),
                'component': simple_component,
                'selected_predictor': predictor,
                'scores': [float(score1), float(score2), float(score3)],
                'probabilities': [float(prob1), float(prob2), float(prob3)]
            })
        
        if predictor_matches:  # Only increment if we found predictor data
            round_num += 1
    
    # Extract reconstruction loss data
    recon_pattern = r"重构损失:\s*\[([\d.,\s']+)\]"
    recon_matches = re.findall(recon_pattern, content)
    
    round_num = 1
    for match in recon_matches:
        # Clean and parse the loss values
        loss_str = match.replace("'", "").replace(" ", "")
        loss_values = [float(x) for x in loss_str.split(',') if x.strip()]
        
        for layer_idx, loss_value in enumerate(loss_values):
            reconstruction_data.append({
                'round': round_num,
                'layer': layer_idx,
                'reconstruction_loss': loss_value
            })
        
        round_num += 1
    
    return (pd.DataFrame(rounds_data), pd.DataFrame(training_data), 
            pd.DataFrame(final_sparsity_data), pd.DataFrame(predictor_data),
            pd.DataFrame(reconstruction_data))

def create_multi_round_sparsity_evolution(rounds_df):
    """Create visualization showing sparsity evolution across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # 1. Sparsity evolution heatmap
    pivot_data = rounds_df.pivot(index='layer', columns='round', values='sparsity')
    
    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlBu_r', 
                center=50, vmin=25, vmax=52, ax=ax1,
                cbar_kws={'label': 'Sparsity (%)'})
    
    ax1.set_title('Multi-Round Sparsity Evolution\n(Layer vs Round)', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Layer Index', fontsize=14)
    
    # 2. Layer-wise sparsity progression lines
    colors = plt.cm.viridis(np.linspace(0, 1, len(pivot_data.index)))
    
    for i, layer in enumerate(pivot_data.index):
        if layer % 4 == 0:  # Show every 4th layer to avoid clutter
            ax2.plot(pivot_data.columns, pivot_data.loc[layer], 'o-', 
                    color=colors[i], linewidth=2, markersize=6, 
                    label=f'Layer {layer}', alpha=0.8)
    
    ax2.set_title('Sparsity Progression by Layer', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Sparsity (%)', fontsize=14)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(20, 55)
    
    plt.tight_layout()
    plt.savefig('./img/multi_round_sparsity_evolution.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_training_loss_analysis(training_df):
    """Create optimized visualization showing training loss across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # 1. Simplified loss progression - show only key rounds
    rounds = sorted(training_df['round'].unique())
    
    # Calculate average loss per round instead of all individual lines
    round_stats = []
    for round_num in rounds:
        round_data = training_df[training_df['round'] == round_num]
        if len(round_data) > 0:
            avg_loss = round_data['loss'].mean()
            min_loss = round_data['loss'].min()
            max_loss = round_data['loss'].max()
            final_loss = round_data[round_data['epoch'] == round_data['epoch'].max()]['loss'].iloc[0]
            round_stats.append({
                'round': round_num,
                'avg_loss': avg_loss,
                'min_loss': min_loss,
                'max_loss': max_loss,
                'final_loss': final_loss
            })
    
    round_stats_df = pd.DataFrame(round_stats)
    
    # Plot average loss with error bars
    ax1.errorbar(round_stats_df['round'], round_stats_df['avg_loss'], 
                yerr=[round_stats_df['avg_loss'] - round_stats_df['min_loss'],
                      round_stats_df['max_loss'] - round_stats_df['avg_loss']], 
                fmt='o-', linewidth=3, markersize=8, capsize=5, capthick=2,
                color='darkblue', label='Average Loss ± Range')
    
    # Plot final loss trend
    ax1.plot(round_stats_df['round'], round_stats_df['final_loss'], 
             's-', linewidth=2, markersize=6, color='red', alpha=0.8,
             label='Final Loss per Round')
    
    ax1.set_title('Training Loss Analysis Across Rounds', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Training Loss', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Loss improvement analysis
    loss_improvements = []
    for i in range(1, len(round_stats_df)):
        prev_loss = round_stats_df.iloc[i-1]['final_loss']
        curr_loss = round_stats_df.iloc[i]['final_loss']
        improvement = ((prev_loss - curr_loss) / prev_loss) * 100
        loss_improvements.append(improvement)
    
    colors = ['green' if x > 0 else 'red' for x in loss_improvements]
    bars = ax2.bar(range(2, len(rounds) + 1), loss_improvements, 
                   color=colors, alpha=0.7, edgecolor='black')
    
    # Add value labels on bars
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height > 0 else -1),
                f'{height:.1f}%', ha='center', va='bottom' if height > 0 else 'top', 
                fontweight='bold')
    
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax2.set_title('Loss Improvement Between Rounds', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Round Transition', fontsize=14)
    ax2.set_ylabel('Loss Improvement (%)', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('./img/training_loss_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_sparsity_convergence_analysis(rounds_df):
    """Analyze how sparsity converges to target across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # 1. Average sparsity per round
    avg_sparsity = rounds_df.groupby('round')['sparsity'].agg(['mean', 'std']).reset_index()
    
    ax1.errorbar(avg_sparsity['round'], avg_sparsity['mean'], 
                yerr=avg_sparsity['std'], fmt='o-', linewidth=3, 
                markersize=8, capsize=5, capthick=2)
    
    # Add target line
    ax1.axhline(y=50, color='red', linestyle='--', linewidth=2, 
               label='Target Sparsity (50%)', alpha=0.8)
    
    ax1.set_title('Sparsity Convergence to Target', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Average Sparsity (%) ± Std', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(20, 55)
    
    # 2. Sparsity variance across layers per round
    sparsity_variance = rounds_df.groupby('round')['sparsity'].var().reset_index()
    
    bars = ax2.bar(sparsity_variance['round'], sparsity_variance['sparsity'], 
                   color='lightcoral', alpha=0.7, edgecolor='darkred')
    
    # Add value labels
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{height:.2f}', ha='center', va='bottom', fontweight='bold')
    
    ax2.set_title('Sparsity Variance Across Layers by Round', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Sparsity Variance', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('./img/sparsity_convergence_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_multi_round_predictor_analysis(predictor_df):
    """Create comprehensive multi-round predictor selection analysis"""

    fig = plt.figure(figsize=(24, 16))
    gs = fig.add_gridspec(3, 4, height_ratios=[2, 1.5, 1.5], width_ratios=[1, 1, 1, 1])

    # 1. Multi-round predictor evolution heatmap (top, spanning all columns)
    ax1 = fig.add_subplot(gs[0, :])

    # Create a comprehensive predictor matrix across all rounds
    rounds = sorted(predictor_df['round'].unique())
    components = sorted(predictor_df['component'].unique())
    layers = sorted(predictor_df['layer'].unique())

    # Create a matrix showing predictor evolution across rounds
    predictor_evolution = []
    for round_num in rounds:
        round_data = predictor_df[predictor_df['round'] == round_num]
        for layer in layers:
            for component in components:
                layer_comp_data = round_data[(round_data['layer'] == layer) &
                                           (round_data['component'] == component)]
                if len(layer_comp_data) > 0:
                    predictor = layer_comp_data['selected_predictor'].iloc[0]
                    predictor_evolution.append({
                        'round': round_num,
                        'layer': layer,
                        'component': component,
                        'predictor': predictor,
                        'position': layer * len(components) + components.index(component)
                    })

    evolution_df = pd.DataFrame(predictor_evolution)

    if len(evolution_df) > 0:
        # Create pivot for heatmap
        pivot_evolution = evolution_df.pivot(index='position', columns='round', values='predictor')

        # Map predictors to numbers
        predictor_map = {'snip': 0, 'wanda': 1, 'dolphin': 2}
        pivot_evolution_num = pivot_evolution.replace(predictor_map)

        # Create colormap
        colors = ['#E74C3C', '#3498DB', '#2ECC71']  # snip=red, wanda=blue, dolphin=green
        cmap = ListedColormap(colors)

        # Plot heatmap
        im = ax1.imshow(pivot_evolution_num.values, cmap=cmap, aspect='auto', vmin=0, vmax=2,
                        interpolation='nearest')

        # Set labels
        ax1.set_xticks(range(len(rounds)))
        ax1.set_xticklabels([f'Round {r}' for r in rounds])

        # Create y-axis labels (Layer-Component combinations)
        y_labels = []
        for layer in layers:
            for comp in components:
                comp_short = comp.replace('attn_', 'A-').replace('mlp_', 'M-').replace('_proj', '')
                y_labels.append(f'L{layer}-{comp_short}')

        ax1.set_yticks(range(0, len(y_labels), 7))  # Show every 7th label (one per layer)
        ax1.set_yticklabels([y_labels[i] for i in range(0, len(y_labels), 7)])

        ax1.set_title('Multi-Round Predictor Evolution Matrix\n(All Layers × Components × Rounds)',
                     fontsize=16, fontweight='bold')
        ax1.set_xlabel('Pruning Round', fontsize=14)
        ax1.set_ylabel('Layer-Component Position', fontsize=14)

        # Add legend
        legend_elements = [mpatches.Patch(color=colors[i], label=pred.upper())
                          for i, pred in enumerate(['SNIP', 'WANDA', 'DOLPHIN'])]
        ax1.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.02, 1))

    # 2. Round-wise predictor distribution (middle left)
    ax2 = fig.add_subplot(gs[1, :2])

    round_predictor_counts = predictor_df.groupby(['round', 'selected_predictor']).size().unstack(fill_value=0)
    round_predictor_pct = round_predictor_counts.div(round_predictor_counts.sum(axis=1), axis=0) * 100

    # Stacked bar chart
    bottom = np.zeros(len(round_predictor_pct))
    for i, predictor in enumerate(['snip', 'wanda', 'dolphin']):
        if predictor in round_predictor_pct.columns:
            values = round_predictor_pct[predictor].values
            ax2.bar(round_predictor_pct.index, values, bottom=bottom,
                   color=colors[i], label=predictor.upper(), alpha=0.8)
            bottom += values

    ax2.set_title('Predictor Distribution Across Rounds', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Pruning Round')
    ax2.set_ylabel('Percentage (%)')
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')

    # 3. Component-wise predictor consistency (middle right)
    ax3 = fig.add_subplot(gs[1, 2:])

    # Calculate predictor consistency for each component across rounds
    component_consistency = []
    for component in components:
        comp_data = predictor_df[predictor_df['component'] == component]
        total_decisions = len(comp_data)
        if total_decisions > 0:
            predictor_counts = comp_data['selected_predictor'].value_counts()
            max_predictor = predictor_counts.index[0]
            max_count = predictor_counts.iloc[0]
            consistency = (max_count / total_decisions) * 100
            component_consistency.append({
                'component': component,
                'dominant_predictor': max_predictor,
                'consistency': consistency
            })

    consistency_df = pd.DataFrame(component_consistency)

    if len(consistency_df) > 0:
        # Color bars by dominant predictor
        bar_colors = [colors[predictor_map[pred]] for pred in consistency_df['dominant_predictor']]

        bars = ax3.bar(range(len(consistency_df)), consistency_df['consistency'],
                      color=bar_colors, alpha=0.7, edgecolor='black')

        ax3.set_title('Component Predictor Consistency', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Component')
        ax3.set_ylabel('Consistency (%)')
        ax3.set_xticks(range(len(consistency_df)))
        ax3.set_xticklabels([comp.replace('_', '\n') for comp in consistency_df['component']],
                           rotation=45, ha='right')
        ax3.grid(True, alpha=0.3, axis='y')

        # Add value labels
        for i, bar in enumerate(bars):
            height = bar.get_height()
            pred = consistency_df.iloc[i]['dominant_predictor'].upper()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{pred}\n{height:.0f}%', ha='center', va='bottom',
                    fontweight='bold', fontsize=9)

    # 4. Predictor confidence evolution (bottom left)
    ax4 = fig.add_subplot(gs[2, :2])

    # Calculate average confidence per round for each predictor
    confidence_evolution = []
    for round_num in rounds:
        round_data = predictor_df[predictor_df['round'] == round_num]
        for predictor in ['snip', 'wanda', 'dolphin']:
            pred_data = round_data[round_data['selected_predictor'] == predictor]
            if len(pred_data) > 0:
                confidences = []
                for _, row in pred_data.iterrows():
                    max_prob_idx = np.argmax(row['probabilities'])
                    confidences.append(row['probabilities'][max_prob_idx])
                avg_confidence = np.mean(confidences)
                confidence_evolution.append({
                    'round': round_num,
                    'predictor': predictor,
                    'avg_confidence': avg_confidence
                })

    confidence_df = pd.DataFrame(confidence_evolution)

    if len(confidence_df) > 0:
        for i, predictor in enumerate(['snip', 'wanda', 'dolphin']):
            pred_data = confidence_df[confidence_df['predictor'] == predictor]
            if len(pred_data) > 0:
                ax4.plot(pred_data['round'], pred_data['avg_confidence'], 'o-',
                        color=colors[i], linewidth=2, markersize=6,
                        label=predictor.upper(), alpha=0.8)

    ax4.set_title('Predictor Confidence Evolution', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Pruning Round')
    ax4.set_ylabel('Average Confidence')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 5. Final round detailed analysis (bottom right)
    ax5 = fig.add_subplot(gs[2, 2:])

    final_round = max(rounds)
    final_data = predictor_df[predictor_df['round'] == final_round]
    final_counts = final_data['selected_predictor'].value_counts()

    wedges, texts, autotexts = ax5.pie(final_counts.values,
                                      labels=[p.upper() for p in final_counts.index],
                                      autopct='%1.1f%%', colors=colors, startangle=90,
                                      explode=(0.05, 0.05, 0.05), shadow=True)

    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')

    ax5.set_title(f'Final Round ({final_round}) Distribution', fontsize=14, fontweight='bold')

    plt.suptitle('Comprehensive Multi-Round Predictor Analysis', fontsize=18, fontweight='bold')
    plt.tight_layout()
    plt.savefig('./img/multi_round_predictor_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
