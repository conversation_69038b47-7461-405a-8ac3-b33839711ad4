#!/usr/bin/env python3
"""
Reconstruction Loss Analysis for Multi-Round Pruning
Analyzes the reconstruction loss patterns across layers and rounds
"""

import re
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import numpy as np
import pandas as pd
import seaborn as sns
import os

# Set style for high-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Create img directory if it doesn't exist
os.makedirs('./img', exist_ok=True)

def parse_reconstruction_loss(filename):
    """Parse reconstruction loss data from log file"""
    
    reconstruction_data = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract reconstruction loss data
    recon_pattern = r"重构损失:\s*\[([\d.,\s']+)\]"
    recon_matches = re.findall(recon_pattern, content)
    
    round_num = 1
    for match in recon_matches:
        # Clean and parse the loss values
        loss_str = match.replace("'", "").replace(" ", "")
        loss_values = [float(x) for x in loss_str.split(',') if x.strip()]
        
        for layer_idx, loss_value in enumerate(loss_values):
            reconstruction_data.append({
                'round': round_num,
                'layer': layer_idx,
                'reconstruction_loss': loss_value
            })
        
        round_num += 1
    
    return pd.DataFrame(reconstruction_data)

def create_reconstruction_loss_heatmap(recon_df):
    """Create heatmap showing reconstruction loss across layers and rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # 1. Reconstruction loss heatmap
    pivot_data = recon_df.pivot(index='layer', columns='round', values='reconstruction_loss')
    
    sns.heatmap(pivot_data, annot=True, fmt='.4f', cmap='Reds', ax=ax1,
                cbar_kws={'label': 'Reconstruction Loss'})
    
    ax1.set_title('Reconstruction Loss Across Layers and Rounds', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Layer Index', fontsize=14)
    
    # 2. Layer-wise reconstruction loss trends
    colors = plt.cm.viridis(np.linspace(0, 1, len(pivot_data.index)))
    
    for i, layer in enumerate(pivot_data.index):
        if layer % 3 == 0:  # Show every 3rd layer to avoid clutter
            ax2.plot(pivot_data.columns, pivot_data.loc[layer], 'o-', 
                    color=colors[i], linewidth=2, markersize=6, 
                    label=f'Layer {layer}', alpha=0.8)
    
    ax2.set_title('Reconstruction Loss Evolution by Layer', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Reconstruction Loss', fontsize=14)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('./img/reconstruction_loss_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_reconstruction_loss_statistics(recon_df):
    """Create statistical analysis of reconstruction loss"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 12))
    
    # 1. Average reconstruction loss per round
    round_stats = recon_df.groupby('round')['reconstruction_loss'].agg(['mean', 'std', 'min', 'max']).reset_index()
    
    ax1.errorbar(round_stats['round'], round_stats['mean'], 
                yerr=round_stats['std'], fmt='o-', linewidth=3, 
                markersize=8, capsize=5, capthick=2, color='darkred')
    
    ax1.fill_between(round_stats['round'], round_stats['min'], round_stats['max'], 
                     alpha=0.2, color='red', label='Min-Max Range')
    
    ax1.set_title('Average Reconstruction Loss per Round', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Pruning Round')
    ax1.set_ylabel('Reconstruction Loss (Mean ± Std)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Reconstruction loss distribution by round
    sns.boxplot(data=recon_df, x='round', y='reconstruction_loss', ax=ax2)
    ax2.set_title('Reconstruction Loss Distribution by Round', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Pruning Round')
    ax2.set_ylabel('Reconstruction Loss')
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. Layer-wise reconstruction loss patterns
    layer_stats = recon_df.groupby('layer')['reconstruction_loss'].agg(['mean', 'std']).reset_index()
    
    bars = ax3.bar(layer_stats['layer'], layer_stats['mean'], 
                   yerr=layer_stats['std'], capsize=3, 
                   color='lightcoral', alpha=0.7, edgecolor='darkred')
    
    ax3.set_title('Average Reconstruction Loss by Layer', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Layer Index')
    ax3.set_ylabel('Reconstruction Loss (Mean ± Std)')
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 4. Reconstruction loss correlation with layer depth
    correlation = recon_df.groupby('layer')['reconstruction_loss'].mean().reset_index()
    
    # Fit polynomial trend line
    z = np.polyfit(correlation['layer'], correlation['reconstruction_loss'], 2)
    p = np.poly1d(z)
    
    ax4.scatter(correlation['layer'], correlation['reconstruction_loss'], 
               color='darkblue', alpha=0.7, s=60)
    ax4.plot(correlation['layer'], p(correlation['layer']), 
             'r--', linewidth=2, alpha=0.8, label='Trend (2nd order)')
    
    ax4.set_title('Reconstruction Loss vs Layer Depth', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Layer Index')
    ax4.set_ylabel('Average Reconstruction Loss')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('./img/reconstruction_loss_statistics.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_reconstruction_loss_insights(recon_df):
    """Create insights visualization for reconstruction loss"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # 1. Reconstruction loss improvement across rounds
    round_improvement = []
    rounds = sorted(recon_df['round'].unique())
    
    for i in range(1, len(rounds)):
        prev_round = recon_df[recon_df['round'] == rounds[i-1]]['reconstruction_loss'].mean()
        curr_round = recon_df[recon_df['round'] == rounds[i]]['reconstruction_loss'].mean()
        improvement = ((prev_round - curr_round) / prev_round) * 100
        round_improvement.append(improvement)
    
    bars = ax1.bar(range(2, len(rounds) + 1), round_improvement, 
                   color=['green' if x > 0 else 'red' for x in round_improvement],
                   alpha=0.7, edgecolor='black')
    
    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height > 0 else -1),
                f'{height:.1f}%', ha='center', va='bottom' if height > 0 else 'top', 
                fontweight='bold')
    
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax1.set_title('Reconstruction Loss Improvement Between Rounds', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Round Transition')
    ax1.set_ylabel('Improvement (%)')
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 2. Critical layers identification (highest reconstruction loss)
    final_round = recon_df['round'].max()
    final_round_data = recon_df[recon_df['round'] == final_round]
    
    # Sort by reconstruction loss and highlight top layers
    sorted_layers = final_round_data.sort_values('reconstruction_loss', ascending=False)
    top_layers = sorted_layers.head(8)  # Top 8 most critical layers
    
    colors = ['red' if layer in top_layers['layer'].values else 'lightblue' 
              for layer in final_round_data['layer']]
    
    bars = ax2.bar(final_round_data['layer'], final_round_data['reconstruction_loss'], 
                   color=colors, alpha=0.7, edgecolor='black')
    
    # Highlight critical layers
    for _, row in top_layers.iterrows():
        ax2.text(row['layer'], row['reconstruction_loss'] + 0.002, 
                f'L{int(row["layer"])}', ha='center', va='bottom', 
                fontweight='bold', fontsize=10)
    
    ax2.set_title(f'Critical Layers Analysis (Round {final_round})', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Layer Index')
    ax2.set_ylabel('Reconstruction Loss')
    ax2.grid(True, alpha=0.3, axis='y')
    
    # Add legend
    red_patch = mpatches.Patch(color='red', alpha=0.7, label='Critical Layers (Top 8)')
    blue_patch = mpatches.Patch(color='lightblue', alpha=0.7, label='Other Layers')
    ax2.legend(handles=[red_patch, blue_patch])
    
    plt.tight_layout()
    plt.savefig('./img/reconstruction_loss_insights.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main analysis function"""
    
    print("🔍 Analyzing Reconstruction Loss Patterns...")
    
    # Parse reconstruction loss data
    recon_df = parse_reconstruction_loss('run.log')
    
    if len(recon_df) == 0:
        print("❌ No reconstruction loss data found in log file")
        return
    
    print(f"📊 Reconstruction Loss Data Summary:")
    print(f"   - Total records: {len(recon_df)}")
    print(f"   - Rounds analyzed: {len(recon_df['round'].unique())}")
    print(f"   - Layers per round: {len(recon_df['layer'].unique())}")
    print(f"   - Loss range: {recon_df['reconstruction_loss'].min():.4f} - {recon_df['reconstruction_loss'].max():.4f}")
    
    print("\n🎨 Creating reconstruction loss visualizations...")
    
    # Create all visualizations
    create_reconstruction_loss_heatmap(recon_df)
    create_reconstruction_loss_statistics(recon_df)
    create_reconstruction_loss_insights(recon_df)
    
    print("\n✅ Reconstruction loss analysis complete!")
    print("📁 Generated files in ./img directory:")
    print("   - reconstruction_loss_analysis.png")
    print("   - reconstruction_loss_statistics.png")
    print("   - reconstruction_loss_insights.png")

if __name__ == "__main__":
    main()
