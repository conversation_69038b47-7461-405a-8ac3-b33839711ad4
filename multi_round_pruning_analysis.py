#!/usr/bin/env python3
"""
Multi-round Pruning Analysis for LOSA
Analyzes the complete multi-round progressive sparsity process
"""

import re
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from collections import defaultdict
import os

# Set style for high-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Create img directory if it doesn't exist
os.makedirs('./img', exist_ok=True)

def parse_multi_round_log(filename):
    """Parse the log file to extract multi-round pruning data"""
    
    rounds_data = []
    training_data = []
    final_sparsity_data = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract training epochs and loss data
    epoch_pattern = r"{'loss': ([\d.]+), 'learning_rate': [\d.e-]+, 'epoch': ([\d.]+)}"
    epoch_matches = re.findall(epoch_pattern, content)
    
    current_round = 0
    for loss, epoch in epoch_matches:
        if float(epoch) == 1.0:  # End of training round
            current_round += 1
        training_data.append({
            'round': current_round,
            'epoch': float(epoch),
            'loss': float(loss)
        })
    
    # Extract round-wise sparsity progression
    round_sparsity_pattern = r'训练后稀疏性检查.*?\n((?:layer \d+ sparsity [\d.]+\n)+)'
    round_matches = re.findall(round_sparsity_pattern, content, re.DOTALL)
    
    for round_idx, sparsity_block in enumerate(round_matches):
        layer_pattern = r'layer (\d+) sparsity ([\d.]+)'
        layer_matches = re.findall(layer_pattern, sparsity_block)
        
        for layer_idx, sparsity in layer_matches:
            rounds_data.append({
                'round': round_idx + 1,
                'layer': int(layer_idx),
                'sparsity': float(sparsity) * 100  # Convert to percentage
            })
    
    # Extract final detailed sparsity data (from the last round)
    final_pattern = r'第(\d+)层剪枝完成汇总:.*?层实际稀疏度: ([\d.]+)%'
    final_matches = re.findall(final_pattern, content)
    
    for layer_idx, sparsity in final_matches:
        final_sparsity_data.append({
            'layer': int(layer_idx),
            'final_sparsity': float(sparsity)
        })
    
    # Extract predictor usage statistics
    predictor_pattern = r'层(\d+)-(.*?): 选择预测器(\w+), 分数=\[([\d.]+), ([\d.]+), ([\d.]+)\], 概率=\[([\d.]+), ([\d.]+), ([\d.]+)\]'
    predictor_matches = re.findall(predictor_pattern, content)
    
    predictor_data = []
    for match in predictor_matches:
        layer_num, component, predictor, score1, score2, score3, prob1, prob2, prob3 = match
        simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
        predictor_data.append({
            'layer': int(layer_num),
            'component': simple_component,
            'selected_predictor': predictor,
            'scores': [float(score1), float(score2), float(score3)],
            'probabilities': [float(prob1), float(prob2), float(prob3)]
        })
    
    return (pd.DataFrame(rounds_data), pd.DataFrame(training_data), 
            pd.DataFrame(final_sparsity_data), pd.DataFrame(predictor_data))

def create_multi_round_sparsity_evolution(rounds_df):
    """Create visualization showing sparsity evolution across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # 1. Sparsity evolution heatmap
    pivot_data = rounds_df.pivot(index='layer', columns='round', values='sparsity')
    
    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlBu_r', 
                center=50, vmin=25, vmax=52, ax=ax1,
                cbar_kws={'label': 'Sparsity (%)'})
    
    ax1.set_title('Multi-Round Sparsity Evolution\n(Layer vs Round)', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Layer Index', fontsize=14)
    
    # 2. Layer-wise sparsity progression lines
    colors = plt.cm.viridis(np.linspace(0, 1, len(pivot_data.index)))
    
    for i, layer in enumerate(pivot_data.index):
        if layer % 4 == 0:  # Show every 4th layer to avoid clutter
            ax2.plot(pivot_data.columns, pivot_data.loc[layer], 'o-', 
                    color=colors[i], linewidth=2, markersize=6, 
                    label=f'Layer {layer}', alpha=0.8)
    
    ax2.set_title('Sparsity Progression by Layer', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Sparsity (%)', fontsize=14)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(20, 55)
    
    plt.tight_layout()
    plt.savefig('./img/multi_round_sparsity_evolution.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_training_loss_analysis(training_df):
    """Create optimized visualization showing training loss across rounds"""

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))

    # 1. Simplified loss progression - show only key rounds
    rounds = sorted(training_df['round'].unique())

    # Calculate average loss per round instead of all individual lines
    round_stats = []
    for round_num in rounds:
        round_data = training_df[training_df['round'] == round_num]
        if len(round_data) > 0:
            avg_loss = round_data['loss'].mean()
            min_loss = round_data['loss'].min()
            max_loss = round_data['loss'].max()
            final_loss = round_data[round_data['epoch'] == round_data['epoch'].max()]['loss'].iloc[0]
            round_stats.append({
                'round': round_num,
                'avg_loss': avg_loss,
                'min_loss': min_loss,
                'max_loss': max_loss,
                'final_loss': final_loss
            })

    round_stats_df = pd.DataFrame(round_stats)

    # Plot average loss with error bars
    ax1.errorbar(round_stats_df['round'], round_stats_df['avg_loss'],
                yerr=[round_stats_df['avg_loss'] - round_stats_df['min_loss'],
                      round_stats_df['max_loss'] - round_stats_df['avg_loss']],
                fmt='o-', linewidth=3, markersize=8, capsize=5, capthick=2,
                color='darkblue', label='Average Loss ± Range')

    # Plot final loss trend
    ax1.plot(round_stats_df['round'], round_stats_df['final_loss'],
             's-', linewidth=2, markersize=6, color='red', alpha=0.8,
             label='Final Loss per Round')

    ax1.set_title('Training Loss Analysis Across Rounds', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Training Loss', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. Loss improvement analysis
    loss_improvements = []
    for i in range(1, len(round_stats_df)):
        prev_loss = round_stats_df.iloc[i-1]['final_loss']
        curr_loss = round_stats_df.iloc[i]['final_loss']
        improvement = ((prev_loss - curr_loss) / prev_loss) * 100
        loss_improvements.append(improvement)

    colors = ['green' if x > 0 else 'red' for x in loss_improvements]
    bars = ax2.bar(range(2, len(rounds) + 1), loss_improvements,
                   color=colors, alpha=0.7, edgecolor='black')

    # Add value labels on bars
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height > 0 else -1),
                f'{height:.1f}%', ha='center', va='bottom' if height > 0 else 'top',
                fontweight='bold')

    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax2.set_title('Loss Improvement Between Rounds', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Round Transition', fontsize=14)
    ax2.set_ylabel('Loss Improvement (%)', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    plt.savefig('./img/training_loss_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_sparsity_convergence_analysis(rounds_df):
    """Analyze how sparsity converges to target across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # 1. Average sparsity per round
    avg_sparsity = rounds_df.groupby('round')['sparsity'].agg(['mean', 'std']).reset_index()
    
    ax1.errorbar(avg_sparsity['round'], avg_sparsity['mean'], 
                yerr=avg_sparsity['std'], fmt='o-', linewidth=3, 
                markersize=8, capsize=5, capthick=2)
    
    # Add target line
    ax1.axhline(y=50, color='red', linestyle='--', linewidth=2, 
               label='Target Sparsity (50%)', alpha=0.8)
    
    ax1.set_title('Sparsity Convergence to Target', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Average Sparsity (%) ± Std', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(20, 55)
    
    # 2. Sparsity variance across layers per round
    sparsity_variance = rounds_df.groupby('round')['sparsity'].var().reset_index()
    
    bars = ax2.bar(sparsity_variance['round'], sparsity_variance['sparsity'], 
                   color='lightcoral', alpha=0.7, edgecolor='darkred')
    
    # Add value labels
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{height:.2f}', ha='center', va='bottom', fontweight='bold')
    
    ax2.set_title('Sparsity Variance Across Layers by Round', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Sparsity Variance', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('./img/sparsity_convergence_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_multi_round_predictor_analysis(predictor_df):
    """Create multi-round predictor selection analysis"""

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 14))

    # 1. Predictor selection matrix (clean version) - remove duplicates first
    predictor_df_clean = predictor_df.drop_duplicates(subset=['layer', 'component'])
    predictor_matrix = predictor_df_clean.pivot(index='component', columns='layer', values='selected_predictor')

    # Create numerical mapping for predictors
    predictor_map = {'snip': 0, 'wanda': 1, 'dolphin': 2}
    predictor_matrix_num = predictor_matrix.replace(predictor_map)

    # Create custom colormap with better colors
    colors = ['#E74C3C', '#3498DB', '#2ECC71']  # snip=red, wanda=blue, dolphin=green
    from matplotlib.colors import ListedColormap
    cmap = ListedColormap(colors)

    # Plot clean heatmap without text annotations and gaps
    im = ax1.imshow(predictor_matrix_num.values, cmap=cmap, aspect='auto', vmin=0, vmax=2,
                    interpolation='nearest')

    # Set ticks and labels
    ax1.set_xticks(range(len(predictor_matrix.columns)))
    ax1.set_xticklabels(range(len(predictor_matrix.columns)), fontsize=10)
    ax1.set_yticks(range(len(predictor_matrix.index)))

    # Improve component labels
    component_labels = {
        'attn_q_proj': 'Attention Q',
        'attn_k_proj': 'Attention K',
        'attn_v_proj': 'Attention V',
        'attn_o_proj': 'Attention O',
        'mlp_down_proj': 'MLP Down',
        'mlp_gate_proj': 'MLP Gate',
        'mlp_up_proj': 'MLP Up'
    }

    y_labels = [component_labels.get(comp, comp) for comp in predictor_matrix.index]
    ax1.set_yticklabels(y_labels, fontsize=11)

    # Remove grid and set limits
    ax1.grid(False)
    ax1.set_xlim(-0.5, len(predictor_matrix.columns) - 0.5)
    ax1.set_ylim(-0.5, len(predictor_matrix.index) - 0.5)

    ax1.set_title('Predictor Selection Matrix: Layer vs Component', fontsize=16, fontweight='bold', pad=20)
    ax1.set_xlabel('Layer Index', fontsize=13)
    ax1.set_ylabel('Component', fontsize=13)

    # Create custom legend
    import matplotlib.patches as mpatches
    legend_elements = [mpatches.Patch(color=colors[i], label=pred.upper())
                      for i, pred in enumerate(['SNIP', 'WANDA', 'DOLPHIN'])]
    ax1.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1.02, 1),
              frameon=True, fancybox=True, shadow=True)

    # 2. Overall predictor usage pie chart
    predictor_counts = predictor_df['selected_predictor'].value_counts()
    wedges, texts, autotexts = ax2.pie(predictor_counts.values,
                                      labels=[p.upper() for p in predictor_counts.index],
                                      autopct='%1.1f%%', colors=colors, startangle=90,
                                      explode=(0.05, 0.05, 0.05), shadow=True)

    # Improve text styling
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(12)

    for text in texts:
        text.set_fontsize(12)
        text.set_fontweight('bold')

    ax2.set_title('Overall Predictor Distribution', fontsize=14, fontweight='bold', pad=20)

    # 3. Component-wise predictor usage
    component_predictor = predictor_df.groupby(['component', 'selected_predictor']).size().unstack(fill_value=0)
    component_predictor_pct = component_predictor.div(component_predictor.sum(axis=1), axis=0) * 100

    # Create horizontal stacked bar chart
    component_predictor_pct_ordered = component_predictor_pct.reindex(['snip', 'wanda', 'dolphin'], axis=1, fill_value=0)

    ax3 = component_predictor_pct_ordered.plot(kind='barh', stacked=True, ax=ax3,
                                              color=colors, width=0.8)

    ax3.set_title('Predictor Usage by Component (%)', fontsize=14, fontweight='bold', pad=20)
    ax3.set_xlabel('Percentage (%)', fontsize=12)
    ax3.set_ylabel('Component', fontsize=12)

    # Improve y-axis labels
    y_labels = [component_labels.get(comp, comp) for comp in component_predictor_pct.index]
    ax3.set_yticklabels(y_labels)

    ax3.legend(title='Predictor', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3, axis='x')
    ax3.set_xlim(0, 100)

    # 4. Predictor confidence analysis
    predictor_confidence = []
    for _, row in predictor_df.iterrows():
        max_prob_idx = np.argmax(row['probabilities'])
        predictor_confidence.append((row['selected_predictor'], row['probabilities'][max_prob_idx]))

    confidence_df = pd.DataFrame(predictor_confidence, columns=['predictor', 'confidence'])
    sns.boxplot(data=confidence_df, x='predictor', y='confidence', ax=ax4)
    ax4.set_title('Predictor Selection Confidence', fontsize=14, fontweight='bold', pad=20)
    ax4.set_xlabel('Predictor', fontsize=12)
    ax4.set_ylabel('Selection Probability', fontsize=12)
    ax4.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    plt.savefig('./img/multi_round_predictor_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()



def main():
    """Main analysis function"""
    
    print("🔍 Analyzing Multi-Round Progressive Pruning...")
    
    # Parse the log file
    rounds_df, training_df, final_df, predictor_df = parse_multi_round_log('run.log')
    
    print(f"📊 Data Summary:")
    print(f"   - Pruning rounds: {len(rounds_df['round'].unique())}")
    print(f"   - Training epochs: {len(training_df)}")
    print(f"   - Layers analyzed: {len(rounds_df['layer'].unique())}")
    print(f"   - Predictor decisions: {len(predictor_df)}")
    
    print("\n🎨 Creating visualizations...")

    # Create all visualizations
    create_multi_round_sparsity_evolution(rounds_df)
    create_training_loss_analysis(training_df)
    create_sparsity_convergence_analysis(rounds_df)
    create_multi_round_predictor_analysis(predictor_df)

    print("\n✅ Multi-round analysis complete!")
    print("📁 Generated files in ./img directory:")
    print("   - multi_round_sparsity_evolution.png")
    print("   - training_loss_analysis.png")
    print("   - sparsity_convergence_analysis.png")
    print("   - multi_round_predictor_analysis.png")

if __name__ == "__main__":
    main()
