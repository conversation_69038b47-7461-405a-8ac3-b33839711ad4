#!/usr/bin/env python3
"""
Multi-round Pruning Analysis for LOSA
Analyzes the complete multi-round progressive sparsity process
"""

import re
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from collections import defaultdict
import os

# Set style for high-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Create img directory if it doesn't exist
os.makedirs('./img', exist_ok=True)

def parse_multi_round_log(filename):
    """Parse the log file to extract multi-round pruning data"""
    
    rounds_data = []
    training_data = []
    final_sparsity_data = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract training epochs and loss data
    epoch_pattern = r"{'loss': ([\d.]+), 'learning_rate': [\d.e-]+, 'epoch': ([\d.]+)}"
    epoch_matches = re.findall(epoch_pattern, content)
    
    current_round = 0
    for loss, epoch in epoch_matches:
        if float(epoch) == 1.0:  # End of training round
            current_round += 1
        training_data.append({
            'round': current_round,
            'epoch': float(epoch),
            'loss': float(loss)
        })
    
    # Extract round-wise sparsity progression
    round_sparsity_pattern = r'训练后稀疏性检查.*?\n((?:layer \d+ sparsity [\d.]+\n)+)'
    round_matches = re.findall(round_sparsity_pattern, content, re.DOTALL)
    
    for round_idx, sparsity_block in enumerate(round_matches):
        layer_pattern = r'layer (\d+) sparsity ([\d.]+)'
        layer_matches = re.findall(layer_pattern, sparsity_block)
        
        for layer_idx, sparsity in layer_matches:
            rounds_data.append({
                'round': round_idx + 1,
                'layer': int(layer_idx),
                'sparsity': float(sparsity) * 100  # Convert to percentage
            })
    
    # Extract final detailed sparsity data (from the last round)
    final_pattern = r'第(\d+)层剪枝完成汇总:.*?层实际稀疏度: ([\d.]+)%'
    final_matches = re.findall(final_pattern, content)
    
    for layer_idx, sparsity in final_matches:
        final_sparsity_data.append({
            'layer': int(layer_idx),
            'final_sparsity': float(sparsity)
        })
    
    # Extract predictor usage statistics
    predictor_pattern = r'层(\d+)-(.*?): 选择预测器(\w+), 分数=\[([\d.]+), ([\d.]+), ([\d.]+)\], 概率=\[([\d.]+), ([\d.]+), ([\d.]+)\]'
    predictor_matches = re.findall(predictor_pattern, content)
    
    predictor_data = []
    for match in predictor_matches:
        layer_num, component, predictor, score1, score2, score3, prob1, prob2, prob3 = match
        simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
        predictor_data.append({
            'layer': int(layer_num),
            'component': simple_component,
            'selected_predictor': predictor,
            'scores': [float(score1), float(score2), float(score3)],
            'probabilities': [float(prob1), float(prob2), float(prob3)]
        })
    
    return (pd.DataFrame(rounds_data), pd.DataFrame(training_data), 
            pd.DataFrame(final_sparsity_data), pd.DataFrame(predictor_data))

def create_multi_round_sparsity_evolution(rounds_df):
    """Create visualization showing sparsity evolution across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # 1. Sparsity evolution heatmap
    pivot_data = rounds_df.pivot(index='layer', columns='round', values='sparsity')
    
    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlBu_r', 
                center=50, vmin=25, vmax=52, ax=ax1,
                cbar_kws={'label': 'Sparsity (%)'})
    
    ax1.set_title('Multi-Round Sparsity Evolution\n(Layer vs Round)', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Layer Index', fontsize=14)
    
    # 2. Layer-wise sparsity progression lines
    colors = plt.cm.viridis(np.linspace(0, 1, len(pivot_data.index)))
    
    for i, layer in enumerate(pivot_data.index):
        if layer % 4 == 0:  # Show every 4th layer to avoid clutter
            ax2.plot(pivot_data.columns, pivot_data.loc[layer], 'o-', 
                    color=colors[i], linewidth=2, markersize=6, 
                    label=f'Layer {layer}', alpha=0.8)
    
    ax2.set_title('Sparsity Progression by Layer', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Sparsity (%)', fontsize=14)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(20, 55)
    
    plt.tight_layout()
    plt.savefig('./img/multi_round_sparsity_evolution.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_training_loss_analysis(training_df):
    """Create visualization showing training loss across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # 1. Loss progression across all rounds
    rounds = training_df['round'].unique()
    colors = plt.cm.Set1(np.linspace(0, 1, len(rounds)))
    
    for i, round_num in enumerate(rounds):
        round_data = training_df[training_df['round'] == round_num]
        if len(round_data) > 0:
            ax1.plot(round_data['epoch'], round_data['loss'], 'o-', 
                    color=colors[i], linewidth=2, markersize=4, 
                    label=f'Round {round_num}', alpha=0.8)
    
    ax1.set_title('Training Loss Across All Rounds', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Epoch', fontsize=14)
    ax1.set_ylabel('Training Loss', fontsize=14)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 2. Final loss comparison across rounds
    final_losses = []
    for round_num in rounds:
        round_data = training_df[training_df['round'] == round_num]
        if len(round_data) > 0:
            final_loss = round_data[round_data['epoch'] == round_data['epoch'].max()]['loss'].iloc[0]
            final_losses.append(final_loss)
    
    bars = ax2.bar(range(1, len(final_losses) + 1), final_losses, 
                   color=colors[:len(final_losses)], alpha=0.7, edgecolor='black')
    
    # Add value labels on bars
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.3f}', ha='center', va='bottom', fontweight='bold')
    
    ax2.set_title('Final Training Loss by Round', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Final Training Loss', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('./img/training_loss_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_sparsity_convergence_analysis(rounds_df):
    """Analyze how sparsity converges to target across rounds"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # 1. Average sparsity per round
    avg_sparsity = rounds_df.groupby('round')['sparsity'].agg(['mean', 'std']).reset_index()
    
    ax1.errorbar(avg_sparsity['round'], avg_sparsity['mean'], 
                yerr=avg_sparsity['std'], fmt='o-', linewidth=3, 
                markersize=8, capsize=5, capthick=2)
    
    # Add target line
    ax1.axhline(y=50, color='red', linestyle='--', linewidth=2, 
               label='Target Sparsity (50%)', alpha=0.8)
    
    ax1.set_title('Sparsity Convergence to Target', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round', fontsize=14)
    ax1.set_ylabel('Average Sparsity (%) ± Std', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(20, 55)
    
    # 2. Sparsity variance across layers per round
    sparsity_variance = rounds_df.groupby('round')['sparsity'].var().reset_index()
    
    bars = ax2.bar(sparsity_variance['round'], sparsity_variance['sparsity'], 
                   color='lightcoral', alpha=0.7, edgecolor='darkred')
    
    # Add value labels
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{height:.2f}', ha='center', va='bottom', fontweight='bold')
    
    ax2.set_title('Sparsity Variance Across Layers by Round', fontsize=16, fontweight='bold')
    ax2.set_xlabel('Pruning Round', fontsize=14)
    ax2.set_ylabel('Sparsity Variance', fontsize=14)
    ax2.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('./img/sparsity_convergence_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_comprehensive_summary(rounds_df, training_df, final_df, predictor_df):
    """Create a comprehensive summary dashboard"""
    
    fig = plt.figure(figsize=(24, 16))
    gs = fig.add_gridspec(4, 4, height_ratios=[1.5, 1, 1, 1], width_ratios=[1, 1, 1, 1])
    
    # 1. Main sparsity evolution (top, spanning 3 columns)
    ax1 = fig.add_subplot(gs[0, :3])
    pivot_data = rounds_df.pivot(index='layer', columns='round', values='sparsity')
    sns.heatmap(pivot_data, annot=False, cmap='RdYlBu_r', center=50, 
                vmin=25, vmax=52, ax=ax1, cbar_kws={'label': 'Sparsity (%)'})
    ax1.set_title('Multi-Round Sparsity Evolution Matrix', fontsize=16, fontweight='bold')
    ax1.set_xlabel('Pruning Round')
    ax1.set_ylabel('Layer Index')
    
    # 2. Round statistics (top right)
    ax2 = fig.add_subplot(gs[0, 3])
    round_stats = rounds_df.groupby('round')['sparsity'].agg(['mean', 'std']).reset_index()
    ax2.plot(round_stats['round'], round_stats['mean'], 'o-', linewidth=3, markersize=8)
    ax2.fill_between(round_stats['round'], 
                     round_stats['mean'] - round_stats['std'],
                     round_stats['mean'] + round_stats['std'], alpha=0.3)
    ax2.axhline(y=50, color='red', linestyle='--', alpha=0.8)
    ax2.set_title('Sparsity Convergence', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Round')
    ax2.set_ylabel('Sparsity (%)')
    ax2.grid(True, alpha=0.3)
    
    # 3. Training loss progression (middle left)
    ax3 = fig.add_subplot(gs[1, :2])
    rounds = training_df['round'].unique()
    colors = plt.cm.Set1(np.linspace(0, 1, len(rounds)))
    for i, round_num in enumerate(rounds):
        round_data = training_df[training_df['round'] == round_num]
        if len(round_data) > 0:
            ax3.plot(round_data['epoch'], round_data['loss'], 
                    color=colors[i], linewidth=2, alpha=0.7, label=f'R{round_num}')
    ax3.set_title('Training Loss Evolution', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Loss')
    ax3.legend(ncol=2)
    ax3.grid(True, alpha=0.3)
    
    # 4. Final sparsity distribution (middle right)
    ax4 = fig.add_subplot(gs[1, 2:])
    if len(final_df) > 0:
        ax4.bar(final_df['layer'], final_df['final_sparsity'], 
               color='skyblue', alpha=0.7, edgecolor='navy')
        ax4.axhline(y=50, color='red', linestyle='--', alpha=0.8)
        ax4.set_title('Final Sparsity by Layer', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Layer')
        ax4.set_ylabel('Final Sparsity (%)')
        ax4.grid(True, alpha=0.3, axis='y')
    
    # 5. Predictor usage (bottom left)
    ax5 = fig.add_subplot(gs[2, :2])
    if len(predictor_df) > 0:
        predictor_counts = predictor_df['selected_predictor'].value_counts()
        colors_pred = ['#E74C3C', '#3498DB', '#2ECC71']
        ax5.pie(predictor_counts.values, labels=predictor_counts.index, 
               autopct='%1.1f%%', colors=colors_pred, startangle=90)
        ax5.set_title('Predictor Usage Distribution', fontsize=14, fontweight='bold')
    
    # 6. Key statistics (bottom right)
    ax6 = fig.add_subplot(gs[2, 2:])
    ax6.axis('off')
    
    # Calculate statistics
    total_rounds = len(rounds_df['round'].unique())
    final_avg_sparsity = rounds_df[rounds_df['round'] == rounds_df['round'].max()]['sparsity'].mean()
    final_std_sparsity = rounds_df[rounds_df['round'] == rounds_df['round'].max()]['sparsity'].std()
    
    if len(training_df) > 0:
        initial_loss = training_df[training_df['round'] == 1]['loss'].iloc[0] if len(training_df[training_df['round'] == 1]) > 0 else 0
        final_loss = training_df[training_df['round'] == training_df['round'].max()]['loss'].iloc[-1] if len(training_df) > 0 else 0
    else:
        initial_loss = final_loss = 0
    
    stats_text = f"""
Multi-Round Pruning Summary

Total Rounds: {total_rounds}
Target Sparsity: 50.0%
Final Avg Sparsity: {final_avg_sparsity:.2f}%
Final Std Sparsity: {final_std_sparsity:.3f}%

Training Performance:
Initial Loss: {initial_loss:.3f}
Final Loss: {final_loss:.3f}
Loss Reduction: {((initial_loss - final_loss) / initial_loss * 100):.1f}%

Layers Processed: 24
Components per Layer: 7
Total Decisions: {len(predictor_df)}
    """
    
    ax6.text(0.1, 0.9, stats_text, transform=ax6.transAxes, fontsize=12,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    # 7. Bottom methodology text
    ax7 = fig.add_subplot(gs[3, :])
    ax7.axis('off')
    
    methodology_text = """
Multi-Round Progressive Pruning Methodology: The LOSA system performs iterative pruning with training recovery cycles. 
Each round: (1) Applies structured pruning using predictor ensemble, (2) Trains model to recover performance, (3) Evaluates sparsity convergence.
This progressive approach ensures stable convergence to target sparsity while maintaining model quality through continuous adaptation.
    """
    
    ax7.text(0.5, 0.5, methodology_text, transform=ax7.transAxes, fontsize=11,
             horizontalalignment='center', verticalalignment='center', style='italic',
             bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.suptitle('Multi-Round Progressive Pruning Analysis Dashboard', 
                 fontsize=20, fontweight='bold', y=0.98)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.95, bottom=0.05)
    plt.savefig('./img/comprehensive_multi_round_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main analysis function"""
    
    print("🔍 Analyzing Multi-Round Progressive Pruning...")
    
    # Parse the log file
    rounds_df, training_df, final_df, predictor_df = parse_multi_round_log('run.log')
    
    print(f"📊 Data Summary:")
    print(f"   - Pruning rounds: {len(rounds_df['round'].unique())}")
    print(f"   - Training epochs: {len(training_df)}")
    print(f"   - Layers analyzed: {len(rounds_df['layer'].unique())}")
    print(f"   - Predictor decisions: {len(predictor_df)}")
    
    print("\n🎨 Creating visualizations...")
    
    # Create all visualizations
    create_multi_round_sparsity_evolution(rounds_df)
    create_training_loss_analysis(training_df)
    create_sparsity_convergence_analysis(rounds_df)
    create_comprehensive_summary(rounds_df, training_df, final_df, predictor_df)
    
    print("\n✅ Multi-round analysis complete!")
    print("📁 Generated files in ./img directory:")
    print("   - multi_round_sparsity_evolution.png")
    print("   - training_loss_analysis.png")
    print("   - sparsity_convergence_analysis.png")
    print("   - comprehensive_multi_round_analysis.png")

if __name__ == "__main__":
    main()
