#!/usr/bin/env python3
"""
Analyze and visualize pruning results from run.log
Creates high-quality visualizations for sparsity allocation, sublayer allocation, and predictor selection
"""

import re
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from collections import defaultdict, Counter
import matplotlib.patches as mpatches

# Set style for high-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

def parse_log_file(filename):
    """Parse the log file to extract pruning data"""

    layer_data = []
    predictor_data = []
    sparsity_data = []

    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()

    # Extract layer-wise sparsity allocation results
    sparsity_pattern = r'第(\d+)层.*?平衡稀疏度分配结果:(.*?)稀疏度标准差'
    sparsity_matches = re.findall(sparsity_pattern, content, re.DOTALL)

    for layer_idx, allocation_text in sparsity_matches:
        layer_num = int(layer_idx)

        # Extract individual component sparsity
        component_pattern = r'(self_attn\.[qkvo]_proj\.base_layer|mlp\.[a-z_]+\.base_layer): ([\d.]+)%'
        components = re.findall(component_pattern, allocation_text)

        for component, sparsity in components:
            # Simplify component names for better visualization
            simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
            sparsity_data.append({
                'layer': layer_num,
                'component': simple_component,
                'sparsity': float(sparsity)
            })

    # Extract predictor selection data
    predictor_pattern = r'层(\d+)-(.*?): 选择预测器(\w+), 分数=\[([\d.]+), ([\d.]+), ([\d.]+)\], 概率=\[([\d.]+), ([\d.]+), ([\d.]+)\]'
    predictor_matches = re.findall(predictor_pattern, content)

    for match in predictor_matches:
        layer_num, component, predictor, score1, score2, score3, prob1, prob2, prob3 = match
        # Simplify component names
        simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
        predictor_data.append({
            'layer': int(layer_num),
            'component': simple_component,
            'selected_predictor': predictor,
            'scores': [float(score1), float(score2), float(score3)],
            'probabilities': [float(prob1), float(prob2), float(prob3)]
        })

    # Extract layer-wise actual sparsity
    layer_sparsity_pattern = r'第(\d+)层剪枝完成汇总:.*?层实际稀疏度: ([\d.]+)%'
    layer_matches = re.findall(layer_sparsity_pattern, content, re.DOTALL)

    for layer_idx, actual_sparsity in layer_matches:
        layer_data.append({
            'layer': int(layer_idx),
            'actual_sparsity': float(actual_sparsity)
        })

    return pd.DataFrame(sparsity_data), pd.DataFrame(predictor_data), pd.DataFrame(layer_data)

def create_sparsity_heatmap(sparsity_df):
    """Create a heatmap showing sparsity allocation across layers and components"""

    # Check for duplicates and remove them
    sparsity_df = sparsity_df.drop_duplicates(subset=['layer', 'component'])

    # Pivot the data for heatmap
    pivot_data = sparsity_df.pivot(index='component', columns='layer', values='sparsity')

    # Create figure
    fig, ax = plt.subplots(figsize=(16, 8))

    # Create heatmap
    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlBu_r',
                center=50, vmin=47, vmax=52, cbar_kws={'label': 'Sparsity (%)'})

    plt.title('Sparsity Allocation Across Layers and Components', fontsize=16, fontweight='bold')
    plt.xlabel('Layer Index', fontsize=14)
    plt.ylabel('Component', fontsize=14)
    plt.xticks(rotation=0)
    plt.yticks(rotation=0)

    # Create mapping for better labels
    component_mapping = {
        'attn_q_proj': 'Self-Attention Q Projection',
        'attn_k_proj': 'Self-Attention K Projection',
        'attn_v_proj': 'Self-Attention V Projection',
        'attn_o_proj': 'Self-Attention Output Projection',
        'mlp_down_proj': 'MLP Down Projection',
        'mlp_gate_proj': 'MLP Gate Projection',
        'mlp_up_proj': 'MLP Up Projection'
    }

    # Update y-axis labels
    current_labels = [label.get_text() for label in ax.get_yticklabels()]
    new_labels = [component_mapping.get(label, label) for label in current_labels]
    ax.set_yticklabels(new_labels)

    plt.tight_layout()
    plt.savefig('sparsity_allocation_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_component_sparsity_distribution(sparsity_df):
    """Create box plots showing sparsity distribution for each component"""

    fig, ax = plt.subplots(figsize=(14, 8))

    # Create box plot
    sns.boxplot(data=sparsity_df, x='component', y='sparsity', ax=ax)

    plt.title('Sparsity Distribution Across Components', fontsize=16, fontweight='bold')
    plt.xlabel('Component', fontsize=14)
    plt.ylabel('Sparsity (%)', fontsize=14)
    plt.xticks(rotation=45, ha='right')

    # Create mapping for better labels
    component_mapping = {
        'attn_q_proj': 'Self-Attn Q\nProjection',
        'attn_k_proj': 'Self-Attn K\nProjection',
        'attn_v_proj': 'Self-Attn V\nProjection',
        'attn_o_proj': 'Self-Attn O\nProjection',
        'mlp_down_proj': 'MLP Down\nProjection',
        'mlp_gate_proj': 'MLP Gate\nProjection',
        'mlp_up_proj': 'MLP Up\nProjection'
    }

    # Update x-axis labels
    current_labels = [label.get_text() for label in ax.get_xticklabels()]
    new_labels = [component_mapping.get(label, label) for label in current_labels]
    ax.set_xticklabels(new_labels)

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('component_sparsity_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_predictor_selection_analysis(predictor_df):
    """Create visualizations for predictor selection patterns"""

    # Count predictor usage
    predictor_counts = predictor_df['selected_predictor'].value_counts()

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 1. Overall predictor usage pie chart
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    ax1.pie(predictor_counts.values, labels=predictor_counts.index, autopct='%1.1f%%',
            colors=colors, startangle=90)
    ax1.set_title('Overall Predictor Selection Distribution', fontsize=14, fontweight='bold')

    # 2. Predictor usage by component
    component_predictor = predictor_df.groupby(['component', 'selected_predictor']).size().unstack(fill_value=0)
    component_predictor.plot(kind='bar', stacked=True, ax=ax2, color=colors)
    ax2.set_title('Predictor Selection by Component', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Component')
    ax2.set_ylabel('Count')
    ax2.legend(title='Predictor')
    ax2.tick_params(axis='x', rotation=45)

    # 3. Predictor usage across layers
    layer_predictor = predictor_df.groupby(['layer', 'selected_predictor']).size().unstack(fill_value=0)
    layer_predictor.plot(kind='bar', ax=ax3, color=colors)
    ax3.set_title('Predictor Selection Across Layers', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Layer')
    ax3.set_ylabel('Count')
    ax3.legend(title='Predictor')
    ax3.tick_params(axis='x', rotation=0)

    # 4. Average confidence scores by predictor
    predictor_confidence = []
    for _, row in predictor_df.iterrows():
        max_prob_idx = np.argmax(row['probabilities'])
        if max_prob_idx == 0 and row['selected_predictor'] == 'dolphin':
            predictor_confidence.append(('dolphin', row['probabilities'][0]))
        elif max_prob_idx == 1 and row['selected_predictor'] == 'wanda':
            predictor_confidence.append(('wanda', row['probabilities'][1]))
        elif max_prob_idx == 2 and row['selected_predictor'] == 'snip':
            predictor_confidence.append(('snip', row['probabilities'][2]))

    confidence_df = pd.DataFrame(predictor_confidence, columns=['predictor', 'confidence'])
    sns.boxplot(data=confidence_df, x='predictor', y='confidence', ax=ax4)
    ax4.set_title('Predictor Selection Confidence', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Predictor')
    ax4.set_ylabel('Selection Probability')

    plt.tight_layout()
    plt.savefig('predictor_selection_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_layer_sparsity_trends(layer_df, sparsity_df):
    """Create visualizations showing sparsity trends across layers"""

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 1. Overall layer sparsity trend
    ax1.plot(layer_df['layer'], layer_df['actual_sparsity'], 'o-', linewidth=2, markersize=6)
    ax1.set_title('Layer-wise Sparsity Trend', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Layer Index')
    ax1.set_ylabel('Actual Sparsity (%)')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(49, 51)

    # 2. Component-wise sparsity trends
    component_mapping = {
        'attn_q_proj': 'Q Proj',
        'attn_k_proj': 'K Proj',
        'attn_v_proj': 'V Proj',
        'attn_o_proj': 'O Proj',
        'mlp_down_proj': 'Down Proj',
        'mlp_gate_proj': 'Gate Proj',
        'mlp_up_proj': 'Up Proj'
    }

    for component in sparsity_df['component'].unique():
        component_data = sparsity_df[sparsity_df['component'] == component]
        label = component_mapping.get(component, component)
        ax2.plot(component_data['layer'], component_data['sparsity'], 'o-',
                label=label, linewidth=2, markersize=4)

    ax2.set_title('Component-wise Sparsity Trends', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Layer Index')
    ax2.set_ylabel('Sparsity (%)')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('sparsity_trends.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """Main function to run all analyses"""

    print("Parsing log file...")
    sparsity_df, predictor_df, layer_df = parse_log_file('run.log')

    print(f"Loaded data:")
    print(f"- {len(sparsity_df)} sparsity records")
    print(f"- {len(predictor_df)} predictor selections")
    print(f"- {len(layer_df)} layer summaries")

    # Debug: Check for duplicates
    print(f"\nData quality check:")
    print(f"- Sparsity unique combinations: {len(sparsity_df[['layer', 'component']].drop_duplicates())}")
    print(f"- Predictor unique combinations: {len(predictor_df[['layer', 'component']].drop_duplicates())}")

    print(f"\nSample sparsity data:")
    print(sparsity_df.head(10))

    print("\nCreating visualizations...")

    # Create all visualizations
    create_sparsity_heatmap(sparsity_df)
    create_component_sparsity_distribution(sparsity_df)
    create_predictor_selection_analysis(predictor_df)
    create_layer_sparsity_trends(layer_df, sparsity_df)

    print("\nAll visualizations saved successfully!")
    print("Generated files:")
    print("- sparsity_allocation_heatmap.png")
    print("- component_sparsity_distribution.png")
    print("- predictor_selection_analysis.png")
    print("- sparsity_trends.png")

if __name__ == "__main__":
    main()
