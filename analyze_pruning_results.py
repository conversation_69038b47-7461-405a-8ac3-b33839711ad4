#!/usr/bin/env python3
"""
Analyze and visualize pruning results from run.log
Creates high-quality visualizations for sparsity allocation, sublayer allocation, and predictor selection
"""

import re
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from collections import defaultdict, Counter
import matplotlib.patches as mpatches
import os

# Set style for high-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Create img directory if it doesn't exist
os.makedirs('./img', exist_ok=True)

def parse_log_file(filename):
    """Parse the log file to extract pruning data"""

    layer_data = []
    predictor_data = []
    sparsity_data = []

    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()

    # Extract layer-wise sparsity allocation results
    sparsity_pattern = r'第(\d+)层.*?平衡稀疏度分配结果:(.*?)稀疏度标准差'
    sparsity_matches = re.findall(sparsity_pattern, content, re.DOTALL)

    for layer_idx, allocation_text in sparsity_matches:
        layer_num = int(layer_idx)

        # Extract individual component sparsity
        component_pattern = r'(self_attn\.[qkvo]_proj\.base_layer|mlp\.[a-z_]+\.base_layer): ([\d.]+)%'
        components = re.findall(component_pattern, allocation_text)

        for component, sparsity in components:
            # Simplify component names for better visualization
            simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
            sparsity_data.append({
                'layer': layer_num,
                'component': simple_component,
                'sparsity': float(sparsity)
            })

    # Extract predictor selection data
    predictor_pattern = r'层(\d+)-(.*?): 选择预测器(\w+), 分数=\[([\d.]+), ([\d.]+), ([\d.]+)\], 概率=\[([\d.]+), ([\d.]+), ([\d.]+)\]'
    predictor_matches = re.findall(predictor_pattern, content)

    for match in predictor_matches:
        layer_num, component, predictor, score1, score2, score3, prob1, prob2, prob3 = match
        # Simplify component names
        simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
        predictor_data.append({
            'layer': int(layer_num),
            'component': simple_component,
            'selected_predictor': predictor,
            'scores': [float(score1), float(score2), float(score3)],
            'probabilities': [float(prob1), float(prob2), float(prob3)]
        })

    # Extract layer-wise actual sparsity
    layer_sparsity_pattern = r'第(\d+)层剪枝完成汇总:.*?层实际稀疏度: ([\d.]+)%'
    layer_matches = re.findall(layer_sparsity_pattern, content, re.DOTALL)

    for layer_idx, actual_sparsity in layer_matches:
        layer_data.append({
            'layer': int(layer_idx),
            'actual_sparsity': float(actual_sparsity)
        })

    return pd.DataFrame(sparsity_data), pd.DataFrame(predictor_data), pd.DataFrame(layer_data)

def create_sparsity_heatmap(sparsity_df):
    """Create a heatmap showing sparsity allocation across layers and components"""

    # Check for duplicates and remove them
    sparsity_df = sparsity_df.drop_duplicates(subset=['layer', 'component'])

    # Pivot the data for heatmap
    pivot_data = sparsity_df.pivot(index='component', columns='layer', values='sparsity')

    # Create figure
    fig, ax = plt.subplots(figsize=(16, 8))

    # Create heatmap
    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlBu_r',
                center=50, vmin=47, vmax=52, cbar_kws={'label': 'Sparsity (%)'})

    plt.title('Sparsity Allocation Across Layers and Components', fontsize=16, fontweight='bold')
    plt.xlabel('Layer Index', fontsize=14)
    plt.ylabel('Component', fontsize=14)
    plt.xticks(rotation=0)
    plt.yticks(rotation=0)

    # Create mapping for better labels
    component_mapping = {
        'attn_q_proj': 'Self-Attention Q Projection',
        'attn_k_proj': 'Self-Attention K Projection',
        'attn_v_proj': 'Self-Attention V Projection',
        'attn_o_proj': 'Self-Attention Output Projection',
        'mlp_down_proj': 'MLP Down Projection',
        'mlp_gate_proj': 'MLP Gate Projection',
        'mlp_up_proj': 'MLP Up Projection'
    }

    # Update y-axis labels
    current_labels = [label.get_text() for label in ax.get_yticklabels()]
    new_labels = [component_mapping.get(label, label) for label in current_labels]
    ax.set_yticklabels(new_labels)

    plt.tight_layout()
    plt.savefig('./img/sparsity_allocation_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_component_sparsity_distribution(sparsity_df):
    """Create box plots showing sparsity distribution for each component"""

    fig, ax = plt.subplots(figsize=(14, 8))

    # Create box plot
    sns.boxplot(data=sparsity_df, x='component', y='sparsity', ax=ax)

    plt.title('Sparsity Distribution Across Components', fontsize=16, fontweight='bold')
    plt.xlabel('Component', fontsize=14)
    plt.ylabel('Sparsity (%)', fontsize=14)
    plt.xticks(rotation=45, ha='right')

    # Create mapping for better labels
    component_mapping = {
        'attn_q_proj': 'Self-Attn Q\nProjection',
        'attn_k_proj': 'Self-Attn K\nProjection',
        'attn_v_proj': 'Self-Attn V\nProjection',
        'attn_o_proj': 'Self-Attn O\nProjection',
        'mlp_down_proj': 'MLP Down\nProjection',
        'mlp_gate_proj': 'MLP Gate\nProjection',
        'mlp_up_proj': 'MLP Up\nProjection'
    }

    # Update x-axis labels
    current_labels = [label.get_text() for label in ax.get_xticklabels()]
    new_labels = [component_mapping.get(label, label) for label in current_labels]
    ax.set_xticklabels(new_labels)

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('./img/component_sparsity_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_predictor_selection_analysis(predictor_df):
    """Create 2D visualization showing predictor selection for each layer and sublayer"""

    # Create a 2D matrix showing predictor selection
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

    # 1. Main 2D heatmap: Layer vs Component showing selected predictor
    predictor_matrix = predictor_df.pivot(index='component', columns='layer', values='selected_predictor')

    # Create numerical mapping for predictors
    predictor_map = {'snip': 0, 'wanda': 1, 'dolphin': 2}
    predictor_matrix_num = predictor_matrix.replace(predictor_map)

    # Create custom colormap
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']  # snip, wanda, dolphin
    from matplotlib.colors import ListedColormap
    cmap = ListedColormap(colors)

    # Plot heatmap
    im = ax1.imshow(predictor_matrix_num.values, cmap=cmap, aspect='auto', vmin=0, vmax=2)

    # Set ticks and labels
    ax1.set_xticks(range(len(predictor_matrix.columns)))
    ax1.set_xticklabels(predictor_matrix.columns)
    ax1.set_yticks(range(len(predictor_matrix.index)))
    ax1.set_yticklabels([comp.replace('_', '\n') for comp in predictor_matrix.index])

    # Add text annotations
    for i in range(len(predictor_matrix.index)):
        for j in range(len(predictor_matrix.columns)):
            predictor = predictor_matrix.iloc[i, j]
            if pd.notna(predictor):
                ax1.text(j, i, predictor.upper(), ha='center', va='center',
                        fontweight='bold', fontsize=8, color='white')

    ax1.set_title('Predictor Selection Matrix: Layer vs Component', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Layer Index', fontsize=12)
    ax1.set_ylabel('Component', fontsize=12)

    # Create custom legend
    legend_elements = [mpatches.Patch(color=colors[i], label=pred.upper())
                      for i, pred in enumerate(['snip', 'wanda', 'dolphin'])]
    ax1.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))

    # 2. Overall predictor usage pie chart
    predictor_counts = predictor_df['selected_predictor'].value_counts()
    ax2.pie(predictor_counts.values, labels=[p.upper() for p in predictor_counts.index],
            autopct='%1.1f%%', colors=colors, startangle=90)
    ax2.set_title('Overall Predictor Distribution', fontsize=14, fontweight='bold')

    # 3. Component-wise predictor usage
    component_predictor = predictor_df.groupby(['component', 'selected_predictor']).size().unstack(fill_value=0)
    component_predictor_pct = component_predictor.div(component_predictor.sum(axis=1), axis=0) * 100

    # Create stacked bar chart
    bottom = np.zeros(len(component_predictor_pct))
    for i, predictor in enumerate(['snip', 'wanda', 'dolphin']):
        if predictor in component_predictor_pct.columns:
            values = component_predictor_pct[predictor].values
            ax3.bar(range(len(component_predictor_pct)), values, bottom=bottom,
                   color=colors[i], label=predictor.upper())
            bottom += values

    ax3.set_title('Predictor Usage by Component (%)', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Component')
    ax3.set_ylabel('Percentage')
    ax3.set_xticks(range(len(component_predictor_pct)))
    ax3.set_xticklabels([comp.replace('_', '\n') for comp in component_predictor_pct.index], rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. Layer-wise predictor distribution
    layer_predictor = predictor_df.groupby(['layer', 'selected_predictor']).size().unstack(fill_value=0)
    layer_predictor_pct = layer_predictor.div(layer_predictor.sum(axis=1), axis=0) * 100

    # Create stacked area chart
    ax4.stackplot(layer_predictor_pct.index,
                 layer_predictor_pct.get('snip', 0),
                 layer_predictor_pct.get('wanda', 0),
                 layer_predictor_pct.get('dolphin', 0),
                 labels=['SNIP', 'WANDA', 'DOLPHIN'], colors=colors, alpha=0.8)

    ax4.set_title('Predictor Usage Across Layers (%)', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Layer Index')
    ax4.set_ylabel('Percentage')
    ax4.legend(loc='upper right')
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0, 100)

    plt.tight_layout()
    plt.savefig('./img/predictor_selection_2d_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_layer_sparsity_trends(layer_df, sparsity_df):
    """Create visualizations showing sparsity trends across layers"""

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 1. Overall layer sparsity trend
    ax1.plot(layer_df['layer'], layer_df['actual_sparsity'], 'o-', linewidth=2, markersize=6)
    ax1.set_title('Layer-wise Sparsity Trend', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Layer Index')
    ax1.set_ylabel('Actual Sparsity (%)')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(49, 51)

    # 2. Component-wise sparsity trends
    component_mapping = {
        'attn_q_proj': 'Q Proj',
        'attn_k_proj': 'K Proj',
        'attn_v_proj': 'V Proj',
        'attn_o_proj': 'O Proj',
        'mlp_down_proj': 'Down Proj',
        'mlp_gate_proj': 'Gate Proj',
        'mlp_up_proj': 'Up Proj'
    }

    for component in sparsity_df['component'].unique():
        component_data = sparsity_df[sparsity_df['component'] == component]
        label = component_mapping.get(component, component)
        ax2.plot(component_data['layer'], component_data['sparsity'], 'o-',
                label=label, linewidth=2, markersize=4)

    ax2.set_title('Component-wise Sparsity Trends', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Layer Index')
    ax2.set_ylabel('Sparsity (%)')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('./img/sparsity_trends.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """Main function to run all analyses"""

    print("Parsing log file...")
    sparsity_df, predictor_df, layer_df = parse_log_file('run.log')

    print(f"Loaded data:")
    print(f"- {len(sparsity_df)} sparsity records")
    print(f"- {len(predictor_df)} predictor selections")
    print(f"- {len(layer_df)} layer summaries")

    # Debug: Check for duplicates
    print(f"\nData quality check:")
    print(f"- Sparsity unique combinations: {len(sparsity_df[['layer', 'component']].drop_duplicates())}")
    print(f"- Predictor unique combinations: {len(predictor_df[['layer', 'component']].drop_duplicates())}")

    print(f"\nSample sparsity data:")
    print(sparsity_df.head(10))

    print("\nCreating visualizations...")

    # Create all visualizations
    create_sparsity_heatmap(sparsity_df)
    create_component_sparsity_distribution(sparsity_df)
    create_predictor_selection_analysis(predictor_df)
    create_layer_sparsity_trends(layer_df, sparsity_df)

    print("\nAll visualizations saved successfully!")
    print("Generated files in ./img directory:")
    print("- sparsity_allocation_heatmap.png")
    print("- component_sparsity_distribution.png")
    print("- predictor_selection_2d_analysis.png")
    print("- sparsity_trends.png")

if __name__ == "__main__":
    main()
