#!/usr/bin/env python3
"""
Analyze and visualize pruning results from run.log
Creates high-quality visualizations for sparsity allocation, sublayer allocation, and predictor selection
"""

import re
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from collections import defaultdict, Counter
import matplotlib.patches as mpatches
import os

# Set style for high-quality plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Create img directory if it doesn't exist
os.makedirs('./img', exist_ok=True)

def parse_log_file(filename):
    """Parse the log file to extract pruning data"""

    layer_data = []
    predictor_data = []
    sparsity_data = []

    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()

    # Extract layer-wise sparsity allocation results
    sparsity_pattern = r'第(\d+)层.*?平衡稀疏度分配结果:(.*?)稀疏度标准差'
    sparsity_matches = re.findall(sparsity_pattern, content, re.DOTALL)

    for layer_idx, allocation_text in sparsity_matches:
        layer_num = int(layer_idx)

        # Extract individual component sparsity
        component_pattern = r'(self_attn\.[qkvo]_proj\.base_layer|mlp\.[a-z_]+\.base_layer): ([\d.]+)%'
        components = re.findall(component_pattern, allocation_text)

        for component, sparsity in components:
            # Simplify component names for better visualization
            simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
            sparsity_data.append({
                'layer': layer_num,
                'component': simple_component,
                'sparsity': float(sparsity)
            })

    # Extract predictor selection data
    predictor_pattern = r'层(\d+)-(.*?): 选择预测器(\w+), 分数=\[([\d.]+), ([\d.]+), ([\d.]+)\], 概率=\[([\d.]+), ([\d.]+), ([\d.]+)\]'
    predictor_matches = re.findall(predictor_pattern, content)

    for match in predictor_matches:
        layer_num, component, predictor, score1, score2, score3, prob1, prob2, prob3 = match
        # Simplify component names
        simple_component = component.replace('.base_layer', '').replace('self_attn.', 'attn_').replace('mlp.', 'mlp_')
        predictor_data.append({
            'layer': int(layer_num),
            'component': simple_component,
            'selected_predictor': predictor,
            'scores': [float(score1), float(score2), float(score3)],
            'probabilities': [float(prob1), float(prob2), float(prob3)]
        })

    # Extract layer-wise actual sparsity
    layer_sparsity_pattern = r'第(\d+)层剪枝完成汇总:.*?层实际稀疏度: ([\d.]+)%'
    layer_matches = re.findall(layer_sparsity_pattern, content, re.DOTALL)

    for layer_idx, actual_sparsity in layer_matches:
        layer_data.append({
            'layer': int(layer_idx),
            'actual_sparsity': float(actual_sparsity)
        })

    return pd.DataFrame(sparsity_data), pd.DataFrame(predictor_data), pd.DataFrame(layer_data)

def create_sparsity_heatmap(sparsity_df):
    """Create a heatmap showing sparsity allocation across layers and components"""

    # Check for duplicates and remove them
    sparsity_df = sparsity_df.drop_duplicates(subset=['layer', 'component'])

    # Pivot the data for heatmap
    pivot_data = sparsity_df.pivot(index='component', columns='layer', values='sparsity')

    # Create figure with better size
    fig, ax = plt.subplots(figsize=(18, 8))

    # Create heatmap with improved styling
    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlBu_r',
                center=50, vmin=47, vmax=52,
                cbar_kws={'label': 'Sparsity (%)', 'shrink': 0.8},
                linewidths=0.5, linecolor='white',
                annot_kws={'fontsize': 9, 'fontweight': 'bold'})

    plt.title('Sparsity Allocation Across Layers and Components',
              fontsize=18, fontweight='bold', pad=20)
    plt.xlabel('Layer Index', fontsize=14)
    plt.ylabel('Component', fontsize=14)

    # Improve tick labels
    ax.set_xticklabels(ax.get_xticklabels(), rotation=0, fontsize=11)

    # Create mapping for better labels
    component_mapping = {
        'attn_q_proj': 'Attention Q',
        'attn_k_proj': 'Attention K',
        'attn_v_proj': 'Attention V',
        'attn_o_proj': 'Attention O',
        'mlp_down_proj': 'MLP Down',
        'mlp_gate_proj': 'MLP Gate',
        'mlp_up_proj': 'MLP Up'
    }

    # Update y-axis labels
    current_labels = [label.get_text() for label in ax.get_yticklabels()]
    new_labels = [component_mapping.get(label, label) for label in current_labels]
    ax.set_yticklabels(new_labels, rotation=0, fontsize=12)

    plt.tight_layout()
    plt.savefig('./img/sparsity_allocation_heatmap.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_component_sparsity_distribution(sparsity_df):
    """Create box plots showing sparsity distribution for each component"""

    fig, ax = plt.subplots(figsize=(14, 8))

    # Create box plot with better styling
    box_plot = sns.boxplot(data=sparsity_df, x='component', y='sparsity', ax=ax,
                          palette='Set2', linewidth=2)

    # Customize box plot appearance
    for patch in box_plot.artists:
        patch.set_alpha(0.7)

    plt.title('Sparsity Distribution Across Components',
              fontsize=18, fontweight='bold', pad=20)
    plt.xlabel('Component', fontsize=14)
    plt.ylabel('Sparsity (%)', fontsize=14)

    # Create mapping for better labels
    component_mapping = {
        'attn_q_proj': 'Attention\nQ',
        'attn_k_proj': 'Attention\nK',
        'attn_v_proj': 'Attention\nV',
        'attn_o_proj': 'Attention\nO',
        'mlp_down_proj': 'MLP\nDown',
        'mlp_gate_proj': 'MLP\nGate',
        'mlp_up_proj': 'MLP\nUp'
    }

    # Update x-axis labels
    current_labels = [label.get_text() for label in ax.get_xticklabels()]
    new_labels = [component_mapping.get(label, label) for label in current_labels]
    ax.set_xticklabels(new_labels, fontsize=12)

    # Add statistics annotations
    for i, component in enumerate(sparsity_df['component'].unique()):
        component_data = sparsity_df[sparsity_df['component'] == component]['sparsity']
        mean_val = component_data.mean()
        ax.text(i, mean_val + 0.1, f'{mean_val:.1f}%',
               ha='center', va='bottom', fontweight='bold', fontsize=10)

    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('./img/component_sparsity_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_predictor_selection_analysis(predictor_df):
    """Create 2D visualization showing predictor selection for each layer and sublayer"""

    # Create a 2D matrix showing predictor selection
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 14))

    # 1. Main 2D heatmap: Layer vs Component showing selected predictor
    predictor_matrix = predictor_df.pivot(index='component', columns='layer', values='selected_predictor')

    # Create numerical mapping for predictors
    predictor_map = {'snip': 0, 'wanda': 1, 'dolphin': 2}
    predictor_matrix_num = predictor_matrix.replace(predictor_map)

    # Create custom colormap with better colors
    colors = ['#E74C3C', '#3498DB', '#2ECC71']  # snip=red, wanda=blue, dolphin=green
    from matplotlib.colors import ListedColormap
    cmap = ListedColormap(colors)

    # Plot clean heatmap without text annotations
    im = ax1.imshow(predictor_matrix_num.values, cmap=cmap, aspect='auto', vmin=0, vmax=2)

    # Set ticks and labels with better formatting
    ax1.set_xticks(range(0, len(predictor_matrix.columns), 2))  # Show every 2nd layer
    ax1.set_xticklabels(range(0, len(predictor_matrix.columns), 2))
    ax1.set_yticks(range(len(predictor_matrix.index)))

    # Improve component labels
    component_labels = {
        'attn_q_proj': 'Attention Q',
        'attn_k_proj': 'Attention K',
        'attn_v_proj': 'Attention V',
        'attn_o_proj': 'Attention O',
        'mlp_down_proj': 'MLP Down',
        'mlp_gate_proj': 'MLP Gate',
        'mlp_up_proj': 'MLP Up'
    }

    y_labels = [component_labels.get(comp, comp) for comp in predictor_matrix.index]
    ax1.set_yticklabels(y_labels, fontsize=11)

    # Add grid for better readability
    ax1.set_xticks(np.arange(-0.5, len(predictor_matrix.columns), 1), minor=True)
    ax1.set_yticks(np.arange(-0.5, len(predictor_matrix.index), 1), minor=True)
    ax1.grid(which='minor', color='white', linestyle='-', linewidth=1)

    ax1.set_title('Predictor Selection Matrix: Layer vs Component', fontsize=16, fontweight='bold', pad=20)
    ax1.set_xlabel('Layer Index', fontsize=13)
    ax1.set_ylabel('Component', fontsize=13)

    # Create custom legend with better positioning
    legend_elements = [mpatches.Patch(color=colors[i], label=pred.upper())
                      for i, pred in enumerate(['SNIP', 'WANDA', 'DOLPHIN'])]
    ax1.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(1.02, 1),
              frameon=True, fancybox=True, shadow=True)

    # 2. Overall predictor usage pie chart with better styling
    predictor_counts = predictor_df['selected_predictor'].value_counts()
    wedges, texts, autotexts = ax2.pie(predictor_counts.values,
                                      labels=[p.upper() for p in predictor_counts.index],
                                      autopct='%1.1f%%', colors=colors, startangle=90,
                                      explode=(0.05, 0.05, 0.05), shadow=True)

    # Improve text styling
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(12)

    for text in texts:
        text.set_fontsize(12)
        text.set_fontweight('bold')

    ax2.set_title('Overall Predictor Distribution', fontsize=14, fontweight='bold', pad=20)

    # 3. Component-wise predictor usage with improved styling
    component_predictor = predictor_df.groupby(['component', 'selected_predictor']).size().unstack(fill_value=0)
    component_predictor_pct = component_predictor.div(component_predictor.sum(axis=1), axis=0) * 100

    # Create horizontal stacked bar chart for better readability
    component_predictor_pct_ordered = component_predictor_pct.reindex(['snip', 'wanda', 'dolphin'], axis=1, fill_value=0)

    ax3 = component_predictor_pct_ordered.plot(kind='barh', stacked=True, ax=ax3,
                                              color=colors, width=0.8)

    ax3.set_title('Predictor Usage by Component (%)', fontsize=14, fontweight='bold', pad=20)
    ax3.set_xlabel('Percentage (%)', fontsize=12)
    ax3.set_ylabel('Component', fontsize=12)

    # Improve y-axis labels
    y_labels = [component_labels.get(comp, comp) for comp in component_predictor_pct.index]
    ax3.set_yticklabels(y_labels)

    ax3.legend(title='Predictor', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3, axis='x')
    ax3.set_xlim(0, 100)

    # 4. Layer-wise predictor distribution with smoother visualization
    layer_predictor = predictor_df.groupby(['layer', 'selected_predictor']).size().unstack(fill_value=0)
    layer_predictor_pct = layer_predictor.div(layer_predictor.sum(axis=1), axis=0) * 100

    # Create stacked area chart with better styling
    ax4.stackplot(layer_predictor_pct.index,
                 layer_predictor_pct.get('snip', 0),
                 layer_predictor_pct.get('wanda', 0),
                 layer_predictor_pct.get('dolphin', 0),
                 labels=['SNIP', 'WANDA', 'DOLPHIN'], colors=colors, alpha=0.85)

    ax4.set_title('Predictor Usage Across Layers (%)', fontsize=14, fontweight='bold', pad=20)
    ax4.set_xlabel('Layer Index', fontsize=12)
    ax4.set_ylabel('Percentage (%)', fontsize=12)
    ax4.legend(loc='upper right', frameon=True, fancybox=True, shadow=True)
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0, 100)
    ax4.set_xlim(0, 23)

    plt.tight_layout()
    plt.savefig('./img/predictor_selection_2d_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_layer_sparsity_trends(layer_df, sparsity_df):
    """Create visualizations showing sparsity trends across layers"""

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))

    # 1. Overall layer sparsity trend with enhanced styling
    ax1.plot(layer_df['layer'], layer_df['actual_sparsity'], 'o-',
             linewidth=3, markersize=8, color='#2E86AB', markerfacecolor='white',
             markeredgewidth=2, markeredgecolor='#2E86AB')

    # Add trend line
    z = np.polyfit(layer_df['layer'], layer_df['actual_sparsity'], 1)
    p = np.poly1d(z)
    ax1.plot(layer_df['layer'], p(layer_df['layer']), "--", alpha=0.7, color='red', linewidth=2)

    ax1.set_title('Layer-wise Sparsity Trend', fontsize=16, fontweight='bold', pad=20)
    ax1.set_xlabel('Layer Index', fontsize=14)
    ax1.set_ylabel('Actual Sparsity (%)', fontsize=14)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(49, 51)

    # Add statistics text
    mean_sparsity = layer_df['actual_sparsity'].mean()
    std_sparsity = layer_df['actual_sparsity'].std()
    ax1.text(0.02, 0.98, f'Mean: {mean_sparsity:.2f}%\nStd: {std_sparsity:.3f}%',
             transform=ax1.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 2. Component-wise sparsity trends with better colors
    component_mapping = {
        'attn_q_proj': 'Attention Q',
        'attn_k_proj': 'Attention K',
        'attn_v_proj': 'Attention V',
        'attn_o_proj': 'Attention O',
        'mlp_down_proj': 'MLP Down',
        'mlp_gate_proj': 'MLP Gate',
        'mlp_up_proj': 'MLP Up'
    }

    # Use distinct colors for each component
    colors = plt.cm.Set1(np.linspace(0, 1, len(sparsity_df['component'].unique())))

    for i, component in enumerate(sparsity_df['component'].unique()):
        component_data = sparsity_df[sparsity_df['component'] == component]
        label = component_mapping.get(component, component)
        ax2.plot(component_data['layer'], component_data['sparsity'], 'o-',
                label=label, linewidth=2.5, markersize=6, color=colors[i])

    ax2.set_title('Component-wise Sparsity Trends', fontsize=16, fontweight='bold', pad=20)
    ax2.set_xlabel('Layer Index', fontsize=14)
    ax2.set_ylabel('Sparsity (%)', fontsize=14)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('./img/sparsity_trends.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main function to run all analyses"""

    print("Parsing log file...")
    sparsity_df, predictor_df, layer_df = parse_log_file('run.log')

    print(f"Loaded data:")
    print(f"- {len(sparsity_df)} sparsity records")
    print(f"- {len(predictor_df)} predictor selections")
    print(f"- {len(layer_df)} layer summaries")

    # Debug: Check for duplicates
    print(f"\nData quality check:")
    print(f"- Sparsity unique combinations: {len(sparsity_df[['layer', 'component']].drop_duplicates())}")
    print(f"- Predictor unique combinations: {len(predictor_df[['layer', 'component']].drop_duplicates())}")

    print(f"\nSample sparsity data:")
    print(sparsity_df.head(10))

    print("\nCreating visualizations...")

    # Create all visualizations
    create_sparsity_heatmap(sparsity_df)
    create_component_sparsity_distribution(sparsity_df)
    create_predictor_selection_analysis(predictor_df)
    create_layer_sparsity_trends(layer_df, sparsity_df)

    print("\nAll visualizations saved successfully!")
    print("Generated files in ./img directory:")
    print("- sparsity_allocation_heatmap.png")
    print("- component_sparsity_distribution.png")
    print("- predictor_selection_2d_analysis.png")
    print("- sparsity_trends.png")

if __name__ == "__main__":
    main()
